// Word文档提取工具前端脚本
class WordExtractor {
    constructor() {
        this.selectedFile = null;
        this.documentAnalysis = null;
        this.selectedHeading = null;
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const extractBtn = document.getElementById('extractBtn');

        // 文件上传区域点击事件
        uploadArea.addEventListener('click', () => fileInput.click());

        // 文件选择事件
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        uploadArea.addEventListener('drop', (e) => this.handleDrop(e));

        // 分析按钮事件
        analyzeBtn.addEventListener('click', () => this.analyzeDocument());

        // 提取按钮事件
        extractBtn.addEventListener('click', () => this.extractContent());
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        this.processSelectedFile(file);
    }

    handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.currentTarget.classList.remove('dragover');
    }

    handleDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processSelectedFile(files[0]);
        }
    }

    processSelectedFile(file) {
        if (!file) return;

        // 验证文件类型
        const validTypes = ['.doc', '.docx'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        
        if (!validTypes.includes(fileExtension)) {
            this.showAlert('请选择Word文档文件 (.doc 或 .docx)', 'warning');
            return;
        }

        this.selectedFile = file;
        
        // 更新UI
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.innerHTML = `
            <i class="fas fa-file-word fa-3x text-success mb-3"></i>
            <p class="mb-2"><strong>${file.name}</strong></p>
            <p class="text-muted small">文件大小: ${this.formatFileSize(file.size)}</p>
        `;

        // 启用分析按钮
        document.getElementById('analyzeBtn').disabled = false;
    }

    async analyzeDocument() {
        if (!this.selectedFile) {
            this.showAlert('请先选择文件', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const formData = new FormData();
            formData.append('file', this.selectedFile);

            const response = await fetch('/api/wordextraction/analyze', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            this.documentAnalysis = await response.json();
            this.displayAnalysisResult();
            this.showAlert('文档分析完成！', 'success');

        } catch (error) {
            console.error('分析文档时出错:', error);
            this.showAlert(`分析文档时出错: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    displayAnalysisResult() {
        if (!this.documentAnalysis) return;

        // 显示文档信息
        const documentInfo = document.getElementById('documentInfo');
        documentInfo.innerHTML = `
            <div class="mb-2">
                <strong>文档名称:</strong><br>
                <span class="text-muted">${this.documentAnalysis.fileName}</span>
            </div>
            <div class="mb-2">
                <strong>总段落数:</strong> ${this.documentAnalysis.totalParagraphs}
            </div>
            <div class="mb-2">
                <strong>总表格数:</strong> ${this.documentAnalysis.totalTables}
            </div>
            <div class="mb-2">
                <strong>标题数量:</strong> ${this.documentAnalysis.headings.length}
            </div>
            <div class="mb-2">
                <strong>目录项数:</strong> ${this.documentAnalysis.tocEntries.length}
            </div>
            ${this.renderHeadingLevelCounts()}
        `;

        // 显示目录结构
        this.displayTocStructure();

        // 显示结果区域
        document.querySelector('.result-section').style.display = 'block';
    }

    renderHeadingLevelCounts() {
        if (!this.documentAnalysis.headingLevelCounts || Object.keys(this.documentAnalysis.headingLevelCounts).length === 0) {
            return '';
        }

        let html = '<div class="mt-2"><strong>标题级别分布:</strong><br>';
        for (const [level, count] of Object.entries(this.documentAnalysis.headingLevelCounts)) {
            html += `<small class="text-muted">级别 ${level}: ${count} 个</small><br>`;
        }
        html += '</div>';
        return html;
    }

    displayTocStructure() {
        const tocList = document.getElementById('tocList');
        
        if (!this.documentAnalysis.tocEntries || this.documentAnalysis.tocEntries.length === 0) {
            tocList.innerHTML = '<p class="text-muted">未找到目录信息，显示所有标题:</p>';
            this.displayAllHeadings();
            return;
        }

        let html = '';
        this.documentAnalysis.tocEntries.forEach((entry, index) => {
            const levelClass = `level-${Math.min(entry.level, 4)}`;
            html += `
                <div class="heading-item ${levelClass}" data-index="${index}" data-title="${entry.title}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>${entry.title}</span>
                        <small class="text-muted">第${entry.pageNumber}页</small>
                    </div>
                </div>
            `;
        });

        tocList.innerHTML = html;

        // 添加点击事件
        tocList.querySelectorAll('.heading-item').forEach(item => {
            item.addEventListener('click', () => this.selectHeading(item));
        });
    }

    displayAllHeadings() {
        const tocList = document.getElementById('tocList');
        
        if (!this.documentAnalysis.headings || this.documentAnalysis.headings.length === 0) {
            tocList.innerHTML += '<p class="text-muted">未找到标题信息</p>';
            return;
        }

        let html = tocList.innerHTML;
        this.documentAnalysis.headings.forEach((heading, index) => {
            if (heading.isHeading) {
                const levelClass = `level-${Math.min(heading.level, 4)}`;
                html += `
                    <div class="heading-item ${levelClass}" data-index="${index}" data-title="${heading.text}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>${heading.text}</span>
                            <small class="text-muted">${heading.styleName}</small>
                        </div>
                    </div>
                `;
            }
        });

        tocList.innerHTML = html;

        // 添加点击事件
        tocList.querySelectorAll('.heading-item').forEach(item => {
            item.addEventListener('click', () => this.selectHeading(item));
        });
    }

    selectHeading(item) {
        // 移除之前的选择
        document.querySelectorAll('.heading-item').forEach(el => el.classList.remove('selected'));
        
        // 选择当前项
        item.classList.add('selected');
        this.selectedHeading = item.dataset.title;

        // 启用提取按钮
        document.getElementById('extractBtn').disabled = false;
    }

    async extractContent() {
        if (!this.selectedFile || !this.selectedHeading) {
            this.showAlert('请先选择文件和标题', 'warning');
            return;
        }

        this.showLoading(true, '正在提取内容...');

        try {
            const formData = new FormData();
            formData.append('file', this.selectedFile);
            formData.append('TargetTitle', this.selectedHeading);
            formData.append('PreserveFormatting', 'true');
            formData.append('IncludeTables', 'true');
            formData.append('IncludeImages', 'true');

            const response = await fetch('/api/wordextraction/extract', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `${this.selectedHeading.replace(/[/\\:*?"<>|]/g, '_')}.docx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showExtractResult();
            this.showAlert('内容提取成功！文件已开始下载。', 'success');

        } catch (error) {
            console.error('提取内容时出错:', error);
            this.showAlert(`提取内容时出错: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    showExtractResult() {
        const extractResult = document.getElementById('extractResult');
        const extractInfo = document.getElementById('extractInfo');
        
        extractInfo.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-download text-success me-2"></i>
                <div>
                    <strong>提取完成:</strong> ${this.selectedHeading}<br>
                    <small class="text-muted">文件已保存为: ${this.selectedHeading.replace(/[/\\:*?"<>|]/g, '_')}.docx</small>
                </div>
            </div>
        `;
        
        extractResult.style.display = 'block';
    }

    showLoading(show, message = '分析中...') {
        const loadingElements = document.querySelectorAll('.loading');
        loadingElements.forEach(el => {
            el.style.display = show ? 'inline-block' : 'none';
            if (show && message) {
                const span = el.querySelector('span');
                if (span) span.textContent = message;
            }
        });
    }

    showAlert(message, type = 'info') {
        // 移除现有的alert
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        // 创建新的alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 插入到页面顶部
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        // 自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new WordExtractor();
});
