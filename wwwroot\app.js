// Word文档提取工具前端脚本
class WordExtractor {
    constructor() {
        this.selectedFile = null;
        this.documentAnalysis = null;
        this.selectedHeading = null;
        this.selectedHeadings = new Set();
        this.allHeadings = [];
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const analyzeBtn = document.getElementById('analyzeBtn');
        const extractBtn = document.getElementById('extractBtn');
        const batchExtractBtn = document.getElementById('batchExtractBtn');
        const selectAllBtn = document.getElementById('selectAllBtn');
        const clearSelectionBtn = document.getElementById('clearSelectionBtn');
        const searchInput = document.getElementById('searchInput');

        // 文件上传区域点击事件
        uploadArea.addEventListener('click', () => fileInput.click());

        // 文件选择事件
        fileInput.addEventListener('change', (e) => this.handleFileSelect(e));

        // 拖拽事件
        uploadArea.addEventListener('dragover', (e) => this.handleDragOver(e));
        uploadArea.addEventListener('dragleave', (e) => this.handleDragLeave(e));
        uploadArea.addEventListener('drop', (e) => this.handleDrop(e));

        // 分析按钮事件
        analyzeBtn.addEventListener('click', () => this.analyzeDocument());

        // 提取按钮事件
        extractBtn.addEventListener('click', () => this.extractContent());
        batchExtractBtn.addEventListener('click', () => this.batchExtractContent());

        // 选择按钮事件
        selectAllBtn.addEventListener('click', () => this.selectAllHeadings());
        clearSelectionBtn.addEventListener('click', () => this.clearAllSelections());

        // 搜索功能
        searchInput.addEventListener('input', (e) => this.filterHeadings(e.target.value));
    }

    handleFileSelect(event) {
        const file = event.target.files[0];
        this.processSelectedFile(file);
    }

    handleDragOver(event) {
        event.preventDefault();
        event.currentTarget.classList.add('dragover');
    }

    handleDragLeave(event) {
        event.currentTarget.classList.remove('dragover');
    }

    handleDrop(event) {
        event.preventDefault();
        event.currentTarget.classList.remove('dragover');
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            this.processSelectedFile(files[0]);
        }
    }

    processSelectedFile(file) {
        if (!file) return;

        // 验证文件类型
        const validTypes = ['.doc', '.docx'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        
        if (!validTypes.includes(fileExtension)) {
            this.showAlert('请选择Word文档文件 (.doc 或 .docx)', 'warning');
            return;
        }

        this.selectedFile = file;
        
        // 更新UI
        const uploadArea = document.getElementById('uploadArea');
        uploadArea.innerHTML = `
            <i class="fas fa-file-word fa-3x text-success mb-3"></i>
            <p class="mb-2"><strong>${file.name}</strong></p>
            <p class="text-muted small">文件大小: ${this.formatFileSize(file.size)}</p>
        `;

        // 启用分析按钮
        document.getElementById('analyzeBtn').disabled = false;
    }

    async analyzeDocument() {
        if (!this.selectedFile) {
            this.showAlert('请先选择文件', 'warning');
            return;
        }

        this.showLoading(true);

        try {
            const formData = new FormData();
            formData.append('file', this.selectedFile);

            const response = await fetch('/api/wordextraction/analyze', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            this.documentAnalysis = await response.json();
            this.displayAnalysisResult();
            this.showAlert('文档分析完成！', 'success');

        } catch (error) {
            console.error('分析文档时出错:', error);
            this.showAlert(`分析文档时出错: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    displayAnalysisResult() {
        if (!this.documentAnalysis) return;

        // 显示文档信息
        const documentInfo = document.getElementById('documentInfo');
        documentInfo.innerHTML = `
            <div class="mb-2">
                <strong>文档名称:</strong><br>
                <span class="text-muted">${this.documentAnalysis.fileName}</span>
            </div>
            <div class="mb-2">
                <strong>总段落数:</strong> ${this.documentAnalysis.totalParagraphs}
            </div>
            <div class="mb-2">
                <strong>总表格数:</strong> ${this.documentAnalysis.totalTables}
            </div>
            <div class="mb-2">
                <strong>标题数量:</strong> ${this.documentAnalysis.headings.length}
            </div>
            <div class="mb-2">
                <strong>目录项数:</strong> ${this.documentAnalysis.tocEntries.length}
            </div>
            ${this.renderHeadingLevelCounts()}
        `;

        // 显示目录结构
        this.displayTocStructure();

        // 显示结果区域
        document.querySelector('.result-section').style.display = 'block';
    }

    renderHeadingLevelCounts() {
        if (!this.documentAnalysis.headingLevelCounts || Object.keys(this.documentAnalysis.headingLevelCounts).length === 0) {
            return '';
        }

        let html = '<div class="mt-2"><strong>标题级别分布:</strong><br>';
        for (const [level, count] of Object.entries(this.documentAnalysis.headingLevelCounts)) {
            html += `<small class="text-muted">级别 ${level}: ${count} 个</small><br>`;
        }
        html += '</div>';
        return html;
    }

    displayTocStructure() {
        const tocList = document.getElementById('tocList');

        if (!this.documentAnalysis.tocEntries || this.documentAnalysis.tocEntries.length === 0) {
            tocList.innerHTML = '<p class="text-muted">未找到目录信息，显示所有标题:</p>';
            this.displayAllHeadings();
            return;
        }

        this.allHeadings = this.documentAnalysis.tocEntries;
        this.renderHeadingsList(this.allHeadings);
    }

    renderHeadingsList(headings) {
        const tocList = document.getElementById('tocList');

        let html = '';
        headings.forEach((entry, index) => {
            const levelClass = `level-${Math.min(entry.level, 4)}`;
            const isSelected = this.selectedHeadings.has(entry.title);
            html += `
                <div class="heading-item ${levelClass}" data-index="${index}" data-title="${entry.title}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <input type="checkbox" ${isSelected ? 'checked' : ''} data-title="${entry.title}">
                            <span>${entry.title}</span>
                        </div>
                        <small class="text-muted">第${entry.pageNumber}页</small>
                    </div>
                </div>
            `;
        });

        tocList.innerHTML = html;

        // 添加事件监听
        tocList.querySelectorAll('.heading-item').forEach(item => {
            const checkbox = item.querySelector('input[type="checkbox"]');
            const title = item.dataset.title;

            // 复选框事件
            checkbox.addEventListener('change', (e) => {
                e.stopPropagation();
                this.toggleHeadingSelection(title, checkbox.checked);
            });

            // 点击事件
            item.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    checkbox.checked = !checkbox.checked;
                    this.toggleHeadingSelection(title, checkbox.checked);
                }
            });
        });
    }

    displayAllHeadings() {
        const tocList = document.getElementById('tocList');

        if (!this.documentAnalysis.headings || this.documentAnalysis.headings.length === 0) {
            tocList.innerHTML += '<p class="text-muted">未找到标题信息</p>';
            return;
        }

        // 过滤出真正的标题
        const headings = this.documentAnalysis.headings
            .filter(heading => heading.isHeading)
            .map(heading => ({
                title: heading.text,
                level: heading.level,
                pageNumber: '',
                styleName: heading.styleName
            }));

        this.allHeadings = headings;
        this.renderHeadingsList(headings);
    }

    toggleHeadingSelection(title, isSelected) {
        if (isSelected) {
            this.selectedHeadings.add(title);
        } else {
            this.selectedHeadings.delete(title);
        }

        // 更新按钮状态
        this.updateButtonStates();
    }

    selectAllHeadings() {
        this.selectedHeadings.clear();
        this.allHeadings.forEach(heading => {
            this.selectedHeadings.add(heading.title);
        });

        // 更新界面
        this.renderHeadingsList(this.allHeadings);
        this.updateButtonStates();
    }

    clearAllSelections() {
        this.selectedHeadings.clear();

        // 更新界面
        this.renderHeadingsList(this.allHeadings);
        this.updateButtonStates();
    }

    filterHeadings(searchTerm) {
        if (!searchTerm.trim()) {
            this.renderHeadingsList(this.allHeadings);
            return;
        }

        const filteredHeadings = this.allHeadings.filter(heading =>
            heading.title.toLowerCase().includes(searchTerm.toLowerCase())
        );

        this.renderHeadingsList(filteredHeadings);
    }

    updateButtonStates() {
        const extractBtn = document.getElementById('extractBtn');
        const batchExtractBtn = document.getElementById('batchExtractBtn');

        const selectedCount = this.selectedHeadings.size;

        if (selectedCount === 1) {
            extractBtn.disabled = false;
            batchExtractBtn.disabled = true;
            this.selectedHeading = Array.from(this.selectedHeadings)[0];
        } else if (selectedCount > 1) {
            extractBtn.disabled = true;
            batchExtractBtn.disabled = false;
            this.selectedHeading = null;
        } else {
            extractBtn.disabled = true;
            batchExtractBtn.disabled = true;
            this.selectedHeading = null;
        }
    }

    async extractContent() {
        if (!this.selectedFile || !this.selectedHeading) {
            this.showAlert('请先选择文件和标题', 'warning');
            return;
        }

        this.showLoading(true, '正在提取内容...');

        try {
            const formData = new FormData();
            formData.append('file', this.selectedFile);
            formData.append('TargetTitle', this.selectedHeading);
            formData.append('PreserveFormatting', 'true');
            formData.append('IncludeTables', 'true');
            formData.append('IncludeImages', 'true');

            const response = await fetch('/api/wordextraction/extract', {
                method: 'POST',
                body: formData
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 下载文件
            const blob = await response.blob();
            this.downloadFile(blob, `${this.selectedHeading.replace(/[/\\:*?"<>|]/g, '_')}.docx`);

            this.showExtractResult([{
                title: this.selectedHeading,
                success: true,
                message: '提取成功'
            }]);
            this.showAlert('内容提取成功！文件已开始下载。', 'success');

        } catch (error) {
            console.error('提取内容时出错:', error);
            this.showAlert(`提取内容时出错: ${error.message}`, 'danger');
        } finally {
            this.showLoading(false);
        }
    }

    async batchExtractContent() {
        if (!this.selectedFile || this.selectedHeadings.size === 0) {
            this.showAlert('请先选择文件和标题', 'warning');
            return;
        }

        const selectedTitles = Array.from(this.selectedHeadings);
        this.showExtractionProgress(selectedTitles);

        const results = [];
        let completed = 0;

        for (const title of selectedTitles) {
            try {
                this.updateProgressItem(title, 'processing', '正在提取...');

                const formData = new FormData();
                formData.append('file', this.selectedFile);
                formData.append('TargetTitle', title);
                formData.append('PreserveFormatting', 'true');
                formData.append('IncludeTables', 'true');
                formData.append('IncludeImages', 'true');

                const response = await fetch('/api/wordextraction/extract', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const blob = await response.blob();
                    this.downloadFile(blob, `${title.replace(/[/\\:*?"<>|]/g, '_')}.docx`);

                    results.push({
                        title: title,
                        success: true,
                        message: '提取成功'
                    });

                    this.updateProgressItem(title, 'success', '提取成功');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }

            } catch (error) {
                results.push({
                    title: title,
                    success: false,
                    message: error.message
                });

                this.updateProgressItem(title, 'error', `提取失败: ${error.message}`);
            }

            completed++;
            this.updateProgressBar((completed / selectedTitles.length) * 100);

            // 添加延迟避免服务器压力
            if (completed < selectedTitles.length) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // 显示最终结果
        setTimeout(() => {
            this.hideExtractionProgress();
            this.showExtractResult(results);

            const successCount = results.filter(r => r.success).length;
            this.showAlert(`批量提取完成！成功: ${successCount}/${results.length}`,
                          successCount === results.length ? 'success' : 'warning');
        }, 1000);
    }

    downloadFile(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    showExtractionProgress(titles) {
        const progressDiv = document.getElementById('extractionProgress');
        const progressList = document.getElementById('progressList');

        // 显示进度区域
        progressDiv.style.display = 'block';

        // 初始化进度列表
        let html = '';
        titles.forEach(title => {
            html += `
                <div class="progress-item" id="progress-${this.sanitizeId(title)}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>${title}</span>
                        <small class="text-muted">等待中...</small>
                    </div>
                </div>
            `;
        });

        progressList.innerHTML = html;
        this.updateProgressBar(0);
    }

    updateProgressItem(title, status, message) {
        const itemId = `progress-${this.sanitizeId(title)}`;
        const item = document.getElementById(itemId);

        if (item) {
            item.className = `progress-item ${status}`;
            const messageElement = item.querySelector('small');
            messageElement.textContent = message;

            if (status === 'success') {
                messageElement.className = 'text-success';
            } else if (status === 'error') {
                messageElement.className = 'text-danger';
            } else {
                messageElement.className = 'text-primary';
            }
        }
    }

    updateProgressBar(percentage) {
        const progressBar = document.getElementById('progressBar');
        progressBar.style.width = `${percentage}%`;
        progressBar.textContent = `${Math.round(percentage)}%`;
    }

    hideExtractionProgress() {
        const progressDiv = document.getElementById('extractionProgress');
        progressDiv.style.display = 'none';
    }

    sanitizeId(str) {
        return str.replace(/[^a-zA-Z0-9]/g, '_');
    }

    showExtractResult(results) {
        const extractResult = document.getElementById('extractResult');
        const extractInfo = document.getElementById('extractInfo');

        if (results.length === 1) {
            // 单个提取结果
            const result = results[0];
            extractInfo.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-download text-success me-2"></i>
                    <div>
                        <strong>提取完成:</strong> ${result.title}<br>
                        <small class="text-muted">文件已保存为: ${result.title.replace(/[/\\:*?"<>|]/g, '_')}.docx</small>
                    </div>
                </div>
            `;
        } else {
            // 批量提取结果
            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            let html = `
                <div class="mb-3">
                    <h6>批量提取完成</h6>
                    <div class="d-flex gap-3">
                        <span class="badge bg-success">成功: ${successCount}</span>
                        ${failCount > 0 ? `<span class="badge bg-danger">失败: ${failCount}</span>` : ''}
                    </div>
                </div>
                <div class="list-group">
            `;

            results.forEach(result => {
                const iconClass = result.success ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
                html += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas ${iconClass} me-2"></i>
                                ${result.title}
                            </div>
                            <small class="text-muted">${result.message}</small>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            extractInfo.innerHTML = html;
        }

        extractResult.style.display = 'block';
    }

    showLoading(show, message = '分析中...') {
        const loadingElements = document.querySelectorAll('.loading');
        loadingElements.forEach(el => {
            el.style.display = show ? 'inline-block' : 'none';
            if (show && message) {
                const span = el.querySelector('span');
                if (span) span.textContent = message;
            }
        });
    }

    showAlert(message, type = 'info') {
        // 移除现有的alert
        const existingAlert = document.querySelector('.alert');
        if (existingAlert) {
            existingAlert.remove();
        }

        // 创建新的alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // 插入到页面顶部
        const container = document.querySelector('.container');
        container.insertBefore(alertDiv, container.firstChild);

        // 自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new WordExtractor();
});
