#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试提取问题 - 检查是否提取了目录而不是实际内容
"""

import requests
import os
from docx import Document

def debug_extraction_issue():
    """
    调试提取问题
    """
    print("🔍 调试提取问题")
    print("="*50)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return
    
    # 测试标题
    test_title = "1.1 测评目的"
    
    try:
        # 1. 先分析文档，看看目录项和实际标题的区别
        print("📋 1. 分析文档结构...")
        base_url = "http://localhost:5000/api/wordextraction"
        
        with open(source_file, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/analyze", files=files)
        
        if response.status_code == 200:
            analysis = response.json()
            
            print(f"📊 文档分析结果:")
            print(f"   - 总段落数: {analysis['totalParagraphs']}")
            print(f"   - 目录项数: {len(analysis['tocEntries'])}")
            print(f"   - 标题数量: {len(analysis['headings'])}")
            
            # 查找测试标题在目录中的位置
            print(f"\n🔍 查找标题 '{test_title}' 在目录中:")
            found_in_toc = False
            for i, entry in enumerate(analysis['tocEntries']):
                if test_title in entry['title']:
                    print(f"   目录项 {i}: {entry['title']} (页码: {entry['pageNumber']})")
                    found_in_toc = True
            
            if not found_in_toc:
                print(f"   ❌ 在目录中未找到标题 '{test_title}'")
            
            # 查找在所有标题中的位置
            print(f"\n🔍 查找标题 '{test_title}' 在所有标题中:")
            found_in_headings = False
            for i, heading in enumerate(analysis['headings']):
                if test_title in heading['text']:
                    print(f"   标题 {i}: {heading['text']} (样式: {heading['styleName']}, 级别: {heading['level']})")
                    found_in_headings = True
            
            if not found_in_headings:
                print(f"   ❌ 在标题中未找到 '{test_title}'")
        
        # 2. 提取内容并分析
        print(f"\n📤 2. 提取标题内容...")
        
        with open(source_file, 'rb') as f:
            files = {'file': f}
            data = {
                'TargetTitle': test_title,
                'PreserveFormatting': 'true',
                'IncludeTables': 'true'
            }
            response = requests.post(f"{base_url}/extract", files=files, data=data)
        
        if response.status_code == 200:
            # 保存并分析提取的文档
            output_file = f"调试_{test_title.replace('.', '_').replace(' ', '_')}.docx"
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 提取成功: {output_file}")
            
            # 分析提取的内容
            doc = Document(output_file)
            print(f"📄 提取的文档分析:")
            print(f"   - 段落数: {len(doc.paragraphs)}")
            print(f"   - 表格数: {len(doc.tables)}")
            print(f"   - 文件大小: {os.path.getsize(output_file):,} 字节")
            
            print(f"\n📝 提取的内容:")
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if text:
                    print(f"   段落 {i+1}: {text}")
                    if i >= 10:  # 只显示前10个段落
                        print(f"   ... (还有 {len(doc.paragraphs) - i - 1} 个段落)")
                        break
            
            # 检查是否只是目录内容
            all_text = " ".join([p.text for p in doc.paragraphs])
            if "目录" in all_text and len(all_text) < 200:
                print(f"\n⚠️ 警告: 提取的内容疑似只是目录，而不是实际章节内容！")
                print(f"   内容长度: {len(all_text)} 字符")
                print(f"   内容预览: {all_text[:100]}...")
            
        else:
            print(f"❌ 提取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
    
    except Exception as e:
        print(f"❌ 调试过程中出错: {str(e)}")

def analyze_source_document():
    """
    分析源文档，找出实际标题的位置
    """
    print(f"\n📖 3. 分析源文档结构...")
    
    source_file = "测评报告.docx"
    try:
        doc = Document(source_file)
        
        print(f"📊 源文档统计:")
        print(f"   - 总段落数: {len(doc.paragraphs)}")
        print(f"   - 总表格数: {len(doc.tables)}")
        
        # 查找包含"测评目的"的段落
        target_text = "测评目的"
        print(f"\n🔍 查找包含 '{target_text}' 的段落:")
        
        found_paragraphs = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if target_text in text:
                style_name = para.style.name if para.style else "Normal"
                found_paragraphs.append({
                    'index': i,
                    'text': text,
                    'style': style_name
                })
                print(f"   段落 {i}: {text} (样式: {style_name})")
        
        if found_paragraphs:
            # 分析第一个找到的段落周围的内容
            first_found = found_paragraphs[0]
            start_idx = max(0, first_found['index'] - 2)
            end_idx = min(len(doc.paragraphs), first_found['index'] + 10)
            
            print(f"\n📄 段落 {first_found['index']} 周围的内容:")
            for i in range(start_idx, end_idx):
                para = doc.paragraphs[i]
                text = para.text.strip()
                style_name = para.style.name if para.style else "Normal"
                marker = " <-- 目标段落" if i == first_found['index'] else ""
                if text:
                    print(f"   段落 {i}: {text} (样式: {style_name}){marker}")
        
    except Exception as e:
        print(f"❌ 分析源文档时出错: {str(e)}")

def main():
    """
    主调试函数
    """
    print("🐛 Word文档提取问题调试")
    print("="*60)
    
    debug_extraction_issue()
    analyze_source_document()
    
    print(f"\n💡 问题分析:")
    print(f"1. 检查提取的内容是否只是目录项而不是实际章节")
    print(f"2. 检查标题匹配逻辑是否正确")
    print(f"3. 检查内容边界检测是否准确")
    print(f"4. 需要区分目录中的标题和文档正文中的标题")

if __name__ == "__main__":
    main()
