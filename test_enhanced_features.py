#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完善后的Web端功能
包括批量提取、搜索、多选等功能
"""

import requests
import os
import time

def test_batch_extraction():
    """
    测试批量提取功能
    """
    print("🚀 测试批量提取功能")
    print("="*50)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return False
    
    # 要批量提取的标题
    test_titles = [
        "网络安全等级测评基本信息表",
        "测评项目概述",
        "被测对象描述"
    ]
    
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        # 使用批量提取API
        with open(source_file, 'rb') as f:
            files = {'file': f}
            data = {}
            
            # 添加多个标题
            for i, title in enumerate(test_titles):
                data[f'titles[{i}]'] = title
            
            print(f"📤 发送批量提取请求...")
            print(f"   标题数量: {len(test_titles)}")
            for title in test_titles:
                print(f"   - {title}")
            
            response = requests.post(f"{base_url}/extract-batch", files=files, data=data)
        
        if response.status_code == 200:
            results = response.json()
            print(f"\n✅ 批量提取成功!")
            print(f"📊 处理结果:")
            
            success_count = 0
            for result in results:
                status = "✅" if result['success'] else "❌"
                print(f"   {status} {result.get('fileName', 'Unknown')}: {result['message']}")
                if result['success']:
                    success_count += 1
            
            print(f"\n📈 统计: {success_count}/{len(results)} 成功")
            return True
            
        else:
            print(f"❌ 批量提取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

def test_individual_extractions():
    """
    测试单个提取功能
    """
    print("\n🎯 测试单个提取功能")
    print("="*50)
    
    source_file = "测评报告.docx"
    test_titles = [
        "网络安全等级测评基本信息表",
        "1.1 测评目的",
        "2.1 被测对象概述"
    ]
    
    success_count = 0
    
    for title in test_titles:
        print(f"\n📋 测试标题: {title}")
        
        try:
            base_url = "http://localhost:5000/api/wordextraction"
            
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': title,
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存文件
                output_file = f"测试_{title.replace('/', '_').replace('\\', '_').replace(':', '_').replace('.', '_')}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                file_size = os.path.getsize(output_file)
                print(f"   ✅ 提取成功: {output_file}")
                print(f"   📄 文件大小: {file_size:,} 字节")
                success_count += 1
                
            else:
                print(f"   ❌ 提取失败: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ 出错: {str(e)}")
    
    print(f"\n📈 单个提取统计: {success_count}/{len(test_titles)} 成功")
    return success_count == len(test_titles)

def test_document_analysis():
    """
    测试文档分析功能
    """
    print("\n🔍 测试文档分析功能")
    print("="*50)
    
    source_file = "测评报告.docx"
    
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        with open(source_file, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/analyze", files=files)
        
        if response.status_code == 200:
            analysis = response.json()
            
            print(f"✅ 文档分析成功!")
            print(f"📊 分析结果:")
            print(f"   - 总段落数: {analysis['totalParagraphs']}")
            print(f"   - 总表格数: {analysis['totalTables']}")
            print(f"   - 标题数量: {len(analysis['headings'])}")
            print(f"   - 目录项数: {len(analysis['tocEntries'])}")
            
            # 显示前10个目录项
            print(f"\n📑 前10个目录项:")
            for i, entry in enumerate(analysis['tocEntries'][:10]):
                level_indent = "  " * (entry['level'] - 1)
                print(f"   {level_indent}• {entry['title']}")
            
            return True
            
        else:
            print(f"❌ 文档分析失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

def test_api_endpoints():
    """
    测试所有API端点
    """
    print("\n🌐 测试API端点")
    print("="*50)
    
    base_url = "http://localhost:5000/api/wordextraction"
    endpoints = [
        ("GET", "/", "主页"),
        ("GET", "/swagger", "API文档")
    ]
    
    for method, endpoint, description in endpoints:
        try:
            url = f"http://localhost:5000{endpoint}"
            response = requests.get(url)
            
            status = "✅" if response.status_code == 200 else "❌"
            print(f"   {status} {description}: {response.status_code}")
            
        except Exception as e:
            print(f"   ❌ {description}: 连接失败")

def main():
    """
    主测试函数
    """
    print("🧪 完善后的Web端功能测试")
    print("="*60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:5000")
        if response.status_code != 200:
            print("❌ Web服务器未正常运行")
            return
    except:
        print("❌ 无法连接到Web服务器")
        return
    
    print("✅ Web服务器运行正常")
    
    # 运行各项测试
    tests = [
        ("文档分析", test_document_analysis),
        ("单个提取", test_individual_extractions),
        ("批量提取", test_batch_extraction),
        ("API端点", test_api_endpoints)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"开始测试: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 时出错: {str(e)}")
            results.append((test_name, False))
    
    # 显示总结
    print(f"\n{'='*60}")
    print("测试总结")
    print('='*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status}: {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有功能测试通过！Web端功能完善成功。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()
