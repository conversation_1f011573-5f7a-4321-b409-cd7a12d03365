#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终提取功能验证
验证修复后的提取功能是否正确提取实际内容
"""

import requests
import os
from docx import Document

def final_extraction_test():
    """
    最终提取测试
    """
    print("🎯 最终提取功能验证")
    print("="*50)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return False
    
    # 测试用例
    test_cases = [
        {
            "title": "1.1 测评目的",
            "expected_keywords": ["测评", "目的", "安全等级", "技术", "管理"],
            "min_length": 200
        },
        {
            "title": "1.2 测评依据",
            "expected_keywords": ["依据", "标准", "GB", "信息安全"],
            "min_length": 200
        },
        {
            "title": "2.1 被测对象概述",
            "expected_keywords": ["被测对象", "概述", "定级", "业务"],
            "min_length": 100
        },
        {
            "title": "网络安全等级测评基本信息表",
            "expected_keywords": ["基本信息"],
            "min_length": 50,
            "expect_tables": True
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['title']}")
        print("-" * 50)
        
        try:
            # 调用API提取
            base_url = "http://localhost:5000/api/wordextraction"
            
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': test_case['title'],
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存提取的文档
                safe_title = test_case['title'].replace('/', '_').replace('\\', '_').replace(':', '_').replace('.', '_')
                output_file = f"最终验证_{safe_title}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                # 分析提取的内容
                doc = Document(output_file)
                
                print(f"✅ 提取成功: {output_file}")
                print(f"📄 段落数: {len(doc.paragraphs)}")
                print(f"📊 表格数: {len(doc.tables)}")
                print(f"📁 文件大小: {os.path.getsize(output_file):,} 字节")
                
                # 获取所有文本内容
                all_text = ""
                for para in doc.paragraphs:
                    text = para.text.strip()
                    if text:
                        all_text += text + " "
                
                print(f"📝 内容长度: {len(all_text)} 字符")
                
                # 显示内容预览
                preview = all_text[:300] + ("..." if len(all_text) > 300 else "")
                print(f"📖 内容预览: {preview}")
                
                # 验证内容质量
                is_valid = True
                issues = []
                
                # 检查1: 内容长度
                if len(all_text) < test_case['min_length']:
                    is_valid = False
                    issues.append(f"内容长度不足 ({len(all_text)} < {test_case['min_length']})")
                
                # 检查2: 关键词
                missing_keywords = []
                for keyword in test_case['expected_keywords']:
                    if keyword not in all_text:
                        missing_keywords.append(keyword)
                
                if missing_keywords:
                    is_valid = False
                    issues.append(f"缺少关键词: {', '.join(missing_keywords)}")
                
                # 检查3: 表格（如果期望有表格）
                if test_case.get('expect_tables', False) and len(doc.tables) == 0:
                    is_valid = False
                    issues.append("期望包含表格但未找到")
                
                # 检查4: 是否只是目录内容
                if is_only_toc_like(all_text):
                    is_valid = False
                    issues.append("内容疑似只是目录项")
                
                if is_valid:
                    print(f"✅ 验证通过: 内容质量良好")
                    success_count += 1
                else:
                    print(f"❌ 验证失败:")
                    for issue in issues:
                        print(f"   - {issue}")
                
            else:
                print(f"❌ 提取失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"📊 最终验证结果: {success_count}/{len(test_cases)} 通过")
    
    if success_count == len(test_cases):
        print("🎉 所有测试通过！提取功能已完全修复。")
        print("✅ 现在能够正确提取实际内容，而不是目录项。")
        return True
    else:
        print(f"⚠️ {len(test_cases) - success_count} 个测试失败，需要进一步优化。")
        return False

def is_only_toc_like(text):
    """
    判断是否只是类似目录的内容
    """
    # 如果文本很短且包含很多数字和页码，可能是目录
    if len(text) < 100:
        # 检查数字密度
        digit_count = sum(1 for c in text if c.isdigit())
        if digit_count > len(text) * 0.4:  # 数字占比超过40%
            return True
    
    # 检查是否包含典型的目录模式
    import re
    toc_patterns = [
        r'\d+\s*$',  # 以数字结尾
        r'\.\.\.',   # 包含省略号
        r'第\s*\d+\s*页',  # 包含页码
        r'^\d+\.\d+\s+\w+\s+\d+$',  # 典型目录格式
    ]
    
    for pattern in toc_patterns:
        if re.search(pattern, text):
            return True
    
    return False

def test_batch_extraction():
    """
    测试批量提取功能
    """
    print(f"\n🚀 测试批量提取功能")
    print("="*50)
    
    source_file = "测评报告.docx"
    test_titles = [
        "1.1 测评目的",
        "1.2 测评依据",
        "2.1 被测对象概述"
    ]
    
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        with open(source_file, 'rb') as f:
            files = {'file': f}
            data = {}
            
            # 添加多个标题
            for i, title in enumerate(test_titles):
                data[f'titles[{i}]'] = title
            
            print(f"📤 发送批量提取请求...")
            response = requests.post(f"{base_url}/extract-batch", files=files, data=data)
        
        if response.status_code == 200:
            results = response.json()
            print(f"✅ 批量提取成功!")
            
            success_count = 0
            for result in results:
                status = "✅" if result['success'] else "❌"
                print(f"   {status} {result.get('fileName', 'Unknown')}: {result['message']}")
                if result['success']:
                    success_count += 1
            
            print(f"📈 批量提取统计: {success_count}/{len(results)} 成功")
            return success_count == len(results)
            
        else:
            print(f"❌ 批量提取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 批量提取测试出错: {str(e)}")
        return False

def main():
    """
    主测试函数
    """
    print("🔧 Word文档内容提取功能 - 最终验证")
    print("="*60)
    
    # 单个提取测试
    single_success = final_extraction_test()
    
    # 批量提取测试
    batch_success = test_batch_extraction()
    
    print(f"\n{'='*60}")
    print("最终验证总结")
    print('='*60)
    
    if single_success and batch_success:
        print("🎉 所有功能验证通过！")
        print("✅ 单个提取功能正常")
        print("✅ 批量提取功能正常")
        print("✅ 内容提取准确，不再是目录项")
        print("✅ 格式保留完整")
        print("\n🚀 Word文档内容提取工具已完全修复并可投入使用！")
    else:
        print("⚠️ 部分功能仍需优化")
        if not single_success:
            print("❌ 单个提取功能需要改进")
        if not batch_success:
            print("❌ 批量提取功能需要改进")

if __name__ == "__main__":
    main()
