#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取Word文档自带目录中的标题
专门分析目录样式和目录内容
"""

import os
import sys
from docx import Document
import json
import re

def extract_toc_headings(file_path):
    """
    提取Word文档自带目录中的标题
    
    Args:
        file_path (str): Word文档路径
        
    Returns:
        dict: 包含目录标题信息的字典
    """
    try:
        # 打开Word文档
        doc = Document(file_path)
        
        # 存储结果
        result = {
            'file_name': os.path.basename(file_path),
            'toc_headings': [],
            'toc_styles': [],
            'total_paragraphs': len(doc.paragraphs)
        }
        
        print(f"正在分析文档: {file_path}")
        print(f"总段落数: {len(doc.paragraphs)}")
        print("-" * 50)
        
        # 分析每个段落，寻找目录样式
        toc_started = False
        toc_ended = False
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if not text:
                continue
                
            # 获取段落样式
            style_name = paragraph.style.name if paragraph.style else "Normal"
            
            # 检查是否为目录开始
            if any(keyword in text.lower() for keyword in ['目录', 'contents', 'table of contents', '目　录']):
                if len(text) <= 10:  # 目录标题通常很短
                    toc_started = True
                    print(f"发现目录开始: {text}")
                    continue
            
            # 检查是否为目录样式
            is_toc_style = (
                style_name.lower().startswith('toc') or 
                '目录' in style_name or
                'contents' in style_name.lower()
            )
            
            # 如果已经开始目录，检查是否结束
            if toc_started and not toc_ended:
                # 如果遇到非目录样式且内容较长，可能目录已结束
                if not is_toc_style and len(text) > 50:
                    toc_ended = True
                    print(f"目录可能结束于: {text[:30]}...")
            
            # 记录目录样式信息
            if is_toc_style:
                # 解析目录项
                toc_entry = parse_toc_entry(text, style_name)
                if toc_entry:
                    result['toc_headings'].append({
                        'index': i,
                        'text': text,
                        'style': style_name,
                        'title': toc_entry['title'],
                        'page_number': toc_entry['page_number'],
                        'level': toc_entry['level']
                    })
                    print(f"目录项: {toc_entry['title']} -> 第{toc_entry['page_number']}页")
                
                # 记录样式
                if style_name not in [item['style'] for item in result['toc_styles']]:
                    result['toc_styles'].append({
                        'style': style_name,
                        'example_text': text[:50] + ('...' if len(text) > 50 else '')
                    })
        
        return result
        
    except Exception as e:
        print(f"分析文档时出错: {str(e)}")
        return None

def parse_toc_entry(text, style_name):
    """
    解析目录项，提取标题和页码
    
    Args:
        text (str): 目录项文本
        style_name (str): 样式名称
        
    Returns:
        dict: 解析后的目录项信息
    """
    # 常见的目录格式模式
    patterns = [
        r'^(.+?)\s*\.{2,}\s*(\d+)$',  # 标题....页码
        r'^(.+?)\s+(\d+)$',           # 标题 页码
        r'^(.+?)\t+(\d+)$',           # 标题\t页码
        r'^(.+?)\s*…+\s*(\d+)$',      # 标题…页码
    ]
    
    for pattern in patterns:
        match = re.match(pattern, text.strip())
        if match:
            title = match.group(1).strip()
            page_number = match.group(2)
            
            # 确定级别
            level = 1
            if 'toc' in style_name.lower():
                try:
                    level_match = re.search(r'(\d+)', style_name)
                    if level_match:
                        level = int(level_match.group(1))
                except:
                    pass
            
            return {
                'title': title,
                'page_number': page_number,
                'level': level
            }
    
    # 如果没有匹配到标准格式，但是目录样式，尝试简单解析
    if 'toc' in style_name.lower() or '目录' in style_name:
        # 查找数字（可能是页码）
        numbers = re.findall(r'\d+', text)
        if numbers:
            # 假设最后一个数字是页码
            page_number = numbers[-1]
            title = re.sub(r'\s*\d+\s*$', '', text).strip()
            
            level = 1
            try:
                level_match = re.search(r'(\d+)', style_name)
                if level_match:
                    level = int(level_match.group(1))
            except:
                pass
            
            return {
                'title': title,
                'page_number': page_number,
                'level': level
            }
    
    return None

def display_toc_tree(toc_headings):
    """以树状结构显示目录标题"""
    print("\n📋 文档目录结构:")
    print("=" * 60)
    
    for i, entry in enumerate(toc_headings, 1):
        level = entry['level']
        title = entry['title']
        page_number = entry['page_number']
        
        # 根据级别添加缩进
        indent = "  " * (level - 1)
        
        # 根据级别选择不同的符号
        if level == 1:
            symbol = "📁"
        elif level == 2:
            symbol = "📂"
        elif level == 3:
            symbol = "📄"
        else:
            symbol = "📝"
        
        print(f"{indent}{symbol} {title} ...................... {page_number}")

def print_summary(result):
    """打印分析结果摘要"""
    if not result:
        return
        
    print("\n" + "="*60)
    print("目录分析结果摘要")
    print("="*60)
    
    print(f"文档名称: {result['file_name']}")
    print(f"总段落数: {result['total_paragraphs']}")
    print(f"目录项数量: {len(result['toc_headings'])}")
    
    if result['toc_headings']:
        display_toc_tree(result['toc_headings'])
        
        # 按级别统计
        level_counts = {}
        for entry in result['toc_headings']:
            level = entry['level']
            level_counts[level] = level_counts.get(level, 0) + 1
        
        print(f"\n📊 目录级别分布:")
        for level in sorted(level_counts.keys()):
            print(f"  级别 {level}: {level_counts[level]} 个")
    
    if result['toc_styles']:
        print("\n📝 发现的目录样式:")
        for style_info in result['toc_styles']:
            print(f"• {style_info['style']}: {style_info['example_text']}")

def main():
    """主函数"""
    # 查找当前目录下的Word文档
    word_files = []
    for file in os.listdir('.'):
        if file.endswith(('.docx', '.doc')):
            word_files.append(file)
    
    if not word_files:
        print("当前目录下没有找到Word文档")
        return
    
    print(f"找到 {len(word_files)} 个Word文档:")
    for file in word_files:
        print(f"• {file}")
    
    # 分析每个Word文档的目录
    all_results = []
    for word_file in word_files:
        print(f"\n{'='*60}")
        print(f"开始分析目录: {word_file}")
        print('='*60)
        
        result = extract_toc_headings(word_file)
        if result:
            all_results.append(result)
            print_summary(result)
            
            # 保存详细结果到JSON文件
            json_file = f"{os.path.splitext(word_file)[0]}_toc_analysis.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n详细目录分析结果已保存到: {json_file}")
    
    print(f"\n{'='*60}")
    print("目录分析完成!")
    print('='*60)

if __name__ == "__main__":
    main()
