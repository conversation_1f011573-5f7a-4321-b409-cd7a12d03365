<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档标签导出工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .upload-area.has-file {
            border-color: #198754;
            background-color: #d1e7dd;
        }
        .loading {
            display: none;
        }
        .file-info {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }
        .tag-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .copy-btn {
            position: relative;
        }
        .copy-success {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #198754;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .copy-success.show {
            opacity: 1;
        }
        .result-section {
            display: none;
        }
        .tag-group {
            margin-bottom: 20px;
        }
        .level-badge {
            font-size: 0.8em;
            margin-left: 5px;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-tags text-primary"></i>
                    Word文档标签导出工具
                </h1>
                
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> 功能说明</h5>
                    <p class="mb-2">本工具可以分析Word文档中的所有标题，并将其转换为标签格式，方便您复制到模板文档中使用。</p>
                    <ul class="mb-0">
                        <li>自动识别文档中的所有标题</li>
                        <li>生成标准标签格式：<code>{标题}</code></li>
                        <li>支持父子标题格式：<code>{父标题-子标题}</code></li>
                        <li>提供多种导出格式，便于复制使用</li>
                    </ul>
                </div>

                <!-- 文件上传区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-upload"></i> 上传Word文档</h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-file-upload fa-3x text-muted mb-3"></i>
                            <p class="mb-2">选择要分析的Word文档</p>
                            <p class="text-muted small">支持 .docx 和 .doc 格式</p>
                            <input type="file" id="fileInput" accept=".docx,.doc" style="display: none;">
                        </div>
                        <div id="fileInfo" class="file-info" style="display: none;">
                            <i class="fas fa-file-word text-primary"></i>
                            <span id="fileName"></span>
                            <span class="badge bg-success ms-2">已选择</span>
                            <button class="btn btn-primary btn-sm ms-3" id="analyzeBtn">
                                <i class="fas fa-search"></i> 分析标题
                            </button>
                        </div>
                        <div class="loading mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">分析中...</span>
                            </div>
                            <p class="mt-2">正在分析文档标题，请稍候...</p>
                        </div>
                    </div>
                </div>

                <!-- 分析结果 -->
                <div class="result-section" id="resultSection">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-check-circle"></i> 标题分析完成</h5>
                        </div>
                        <div class="card-body">
                            <div id="summaryInfo" class="alert alert-success">
                                <!-- 摘要信息将在这里显示 -->
                            </div>

                            <!-- 标签导出选项 -->
                            <div class="row">
                                <!-- 简单标签格式 -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-tag"></i> 简单标签格式</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="small text-muted">每个标题转换为 {标题} 格式</p>
                                            <div class="tag-output" id="simpleTagsOutput"></div>
                                            <button class="btn btn-outline-primary btn-sm mt-2 copy-btn" onclick="copyToClipboard('simpleTagsOutput', this)">
                                                <i class="fas fa-copy"></i> 复制全部
                                                <div class="copy-success">已复制!</div>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 带注释的标签格式 -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-comment"></i> 带注释标签格式</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="small text-muted">包含标题级别和页码信息</p>
                                            <div class="tag-output" id="commentedTagsOutput"></div>
                                            <button class="btn btn-outline-primary btn-sm mt-2 copy-btn" onclick="copyToClipboard('commentedTagsOutput', this)">
                                                <i class="fas fa-copy"></i> 复制全部
                                                <div class="copy-success">已复制!</div>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 父子标题格式 -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-sitemap"></i> 父子标题格式</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="small text-muted">子标题使用 {父标题-子标题} 格式</p>
                                            <div class="tag-output" id="parentChildTagsOutput"></div>
                                            <button class="btn btn-outline-primary btn-sm mt-2 copy-btn" onclick="copyToClipboard('parentChildTagsOutput', this)">
                                                <i class="fas fa-copy"></i> 复制全部
                                                <div class="copy-success">已复制!</div>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 按级别分组 -->
                                <div class="col-md-6 mb-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6><i class="fas fa-layer-group"></i> 按级别分组</h6>
                                        </div>
                                        <div class="card-body">
                                            <p class="small text-muted">按标题级别分组显示</p>
                                            <div id="groupedTagsOutput"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div class="alert alert-danger" id="errorAlert" style="display: none;">
                    <h6><i class="fas fa-exclamation-triangle"></i> 分析失败</h6>
                    <p id="errorMessage" class="mb-0"></p>
                </div>

                <!-- 返回链接 -->
                <div class="text-center mt-4">
                    <a href="index.html" class="btn btn-outline-primary me-2">
                        <i class="fas fa-arrow-left"></i> 返回内容提取
                    </a>
                    <a href="template.html" class="btn btn-outline-success">
                        <i class="fas fa-magic"></i> 模板替换工具
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="export-tags.js"></script>
</body>
</html>
