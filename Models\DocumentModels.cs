namespace WordExtractor.Models
{
    /// <summary>
    /// 文档分析结果
    /// </summary>
    public class DocumentAnalysisResult
    {
        public string FileName { get; set; } = string.Empty;
        public int TotalParagraphs { get; set; }
        public int TotalTables { get; set; }
        public List<DocumentHeading> Headings { get; set; } = new();
        public List<TocEntry> TocEntries { get; set; } = new();
        public Dictionary<int, int> HeadingLevelCounts { get; set; } = new();
    }

    /// <summary>
    /// 文档标题
    /// </summary>
    public class DocumentHeading
    {
        public int Index { get; set; }
        public string Text { get; set; } = string.Empty;
        public string StyleName { get; set; } = string.Empty;
        public int Level { get; set; }
        public bool IsHeading { get; set; }
        public string? PageNumber { get; set; }
    }

    /// <summary>
    /// 目录项
    /// </summary>
    public class TocEntry
    {
        public int Index { get; set; }
        public string Text { get; set; } = string.Empty;
        public string StyleName { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public string PageNumber { get; set; } = string.Empty;
        public int Level { get; set; }
    }

    /// <summary>
    /// 提取请求
    /// </summary>
    public class ExtractionRequest
    {
        public string TargetTitle { get; set; } = string.Empty;
        public bool PreserveFormatting { get; set; } = true;
        public bool IncludeTables { get; set; } = true;
        public bool IncludeImages { get; set; } = true;
    }

    /// <summary>
    /// 提取结果
    /// </summary>
    public class ExtractionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? FileName { get; set; }
        public byte[]? DocumentBytes { get; set; }
        public int ExtractedParagraphs { get; set; }
        public int ExtractedTables { get; set; }
    }
}
