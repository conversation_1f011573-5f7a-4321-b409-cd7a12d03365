using Microsoft.AspNetCore.Mvc;
using WordExtractor.Models;
using WordExtractor.Services;

namespace WordExtractor.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class WordExtractionController : ControllerBase
    {
        private readonly IWordExtractionService _wordExtractionService;
        private readonly ILogger<WordExtractionController> _logger;

        public WordExtractionController(
            IWordExtractionService wordExtractionService,
            ILogger<WordExtractionController> logger)
        {
            _wordExtractionService = wordExtractionService;
            _logger = logger;
        }

        /// <summary>
        /// 分析Word文档，获取目录和标题信息
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <returns>文档分析结果</returns>
        [HttpPost("analyze")]
        public async Task<ActionResult<DocumentAnalysisResult>> AnalyzeDocument(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请选择一个有效的Word文档文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                using var stream = file.OpenReadStream();
                var result = await _wordExtractionService.AnalyzeDocumentAsync(stream);
                result.FileName = file.FileName;

                _logger.LogInformation($"成功分析文档: {file.FileName}, 段落数: {result.TotalParagraphs}, 标题数: {result.Headings.Count}");

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"分析文档时出错: {file?.FileName}");
                return StatusCode(500, $"分析文档时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取文档中的所有标题
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <returns>标题列表</returns>
        [HttpPost("headings")]
        public async Task<ActionResult<List<DocumentHeading>>> GetHeadings(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请选择一个有效的Word文档文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                using var stream = file.OpenReadStream();
                var headings = await _wordExtractionService.GetDocumentHeadingsAsync(stream);

                _logger.LogInformation($"成功获取文档标题: {file.FileName}, 标题数: {headings.Count}");

                return Ok(headings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取文档标题时出错: {file?.FileName}");
                return StatusCode(500, $"获取文档标题时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 提取指定标题的内容，保留原格式
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <param name="request">提取请求参数</param>
        /// <returns>提取的Word文档</returns>
        [HttpPost("extract")]
        public async Task<IActionResult> ExtractSection(IFormFile file, [FromForm] ExtractionRequest request)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请选择一个有效的Word文档文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                if (string.IsNullOrWhiteSpace(request.TargetTitle))
                    return BadRequest("请指定要提取的标题");

                using var stream = file.OpenReadStream();
                var extractedBytes = await _wordExtractionService.ExtractSectionWithFormatAsync(stream, request.TargetTitle);

                var fileName = $"{SanitizeFileName(request.TargetTitle)}.docx";

                _logger.LogInformation($"成功提取内容: {file.FileName} -> {fileName}");

                return File(extractedBytes, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"提取内容时出错: {file?.FileName}, 标题: {request?.TargetTitle}");
                return StatusCode(500, $"提取内容时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量提取多个标题的内容
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <param name="titles">要提取的标题列表</param>
        /// <returns>提取结果列表</returns>
        [HttpPost("extract-batch")]
        public async Task<ActionResult<List<ExtractionResult>>> ExtractMultipleSections(
            IFormFile file, 
            [FromForm] string[] titles)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请选择一个有效的Word文档文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                if (titles == null || titles.Length == 0)
                    return BadRequest("请指定要提取的标题列表");

                var results = new List<ExtractionResult>();

                foreach (var title in titles)
                {
                    try
                    {
                        using var stream = file.OpenReadStream();
                        var extractedBytes = await _wordExtractionService.ExtractSectionWithFormatAsync(stream, title);
                        
                        var fileName = $"{SanitizeFileName(title)}.docx";
                        
                        results.Add(new ExtractionResult
                        {
                            Success = true,
                            Message = "提取成功",
                            FileName = fileName,
                            DocumentBytes = extractedBytes
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new ExtractionResult
                        {
                            Success = false,
                            Message = ex.Message,
                            FileName = title
                        });
                    }
                }

                _logger.LogInformation($"批量提取完成: {file.FileName}, 成功: {results.Count(r => r.Success)}/{results.Count}");

                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"批量提取时出错: {file?.FileName}");
                return StatusCode(500, $"批量提取时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 替换模板文档中的标签为参考文档中的对应内容
        /// </summary>
        /// <param name="templateFile">模板文档文件</param>
        /// <param name="referenceFile">参考文档文件</param>
        /// <returns>替换后的文档</returns>
        [HttpPost("replace-template")]
        public async Task<IActionResult> ReplaceTemplate(IFormFile templateFile, IFormFile referenceFile)
        {
            try
            {
                if (templateFile == null || templateFile.Length == 0)
                    return BadRequest("请上传模板文件");

                if (referenceFile == null || referenceFile.Length == 0)
                    return BadRequest("请上传参考文件");

                if (!IsWordDocument(templateFile.FileName))
                    return BadRequest("模板文件必须是Word文档 (.docx 或 .doc)");

                if (!IsWordDocument(referenceFile.FileName))
                    return BadRequest("参考文件必须是Word文档 (.docx 或 .doc)");

                using var templateStream = templateFile.OpenReadStream();
                using var referenceStream = referenceFile.OpenReadStream();

                var resultBytes = await _wordExtractionService.ReplaceTemplateTagsAsync(templateStream, referenceStream);

                var fileName = $"替换结果_{Path.GetFileNameWithoutExtension(templateFile.FileName)}.docx";

                _logger.LogInformation($"模板替换完成: {templateFile.FileName} + {referenceFile.FileName} -> {fileName}");

                return File(resultBytes, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "模板替换过程中发生错误");
                return StatusCode(500, $"模板替换失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 导出文档中的所有标题作为标签格式
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <returns>标签列表</returns>
        [HttpPost("export-tags")]
        public async Task<IActionResult> ExportTags(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请上传文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                using var stream = file.OpenReadStream();
                var headings = await _wordExtractionService.GetDocumentHeadingsAsync(stream);

                // 转换为标签格式
                var tagExport = new
                {
                    FileName = file.FileName,
                    TotalHeadings = headings.Count,
                    Tags = headings.Select(h => new
                    {
                        OriginalTitle = h.Title,
                        Tag = $"{{{h.Title}}}",
                        Level = h.Level,
                        PageNumber = h.PageNumber,
                        StyleName = h.StyleName
                    }).ToList(),
                    TagsGroupedByLevel = headings
                        .GroupBy(h => h.Level)
                        .OrderBy(g => g.Key)
                        .Select(g => new
                        {
                            Level = g.Key,
                            Count = g.Count(),
                            Tags = g.Select(h => $"{{{h.Title}}}").ToList()
                        }).ToList(),
                    AllTagsText = string.Join("\n", headings.Select(h => $"{{{h.Title}}}")),
                    AllTagsWithComments = string.Join("\n", headings.Select(h => $"{{{h.Title}}} <!-- {h.Level}级标题，第{h.PageNumber}页 -->")),
                    ParentChildTags = headings
                        .Where(h => h.Level >= 2)
                        .Select(h =>
                        {
                            // 查找父标题
                            var parentHeading = headings
                                .Where(p => p.Level < h.Level && p.PageNumber <= h.PageNumber)
                                .OrderByDescending(p => p.PageNumber)
                                .ThenByDescending(p => p.Level)
                                .FirstOrDefault();

                            return new
                            {
                                ChildTitle = h.Title,
                                ParentTitle = parentHeading?.Title,
                                Tag = parentHeading != null ? $"{{{parentHeading.Title}-{h.Title}}}" : $"{{{h.Title}}}",
                                Level = h.Level,
                                PageNumber = h.PageNumber
                            };
                        })
                        .Where(t => t.ParentTitle != null)
                        .ToList()
                };

                _logger.LogInformation($"标签导出完成: {file.FileName}, 共{headings.Count}个标题");

                return Ok(tagExport);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "标签导出过程中发生错误");
                return StatusCode(500, $"标签导出失败: {ex.Message}");
            }
        }

        private static bool IsWordDocument(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension == ".docx" || extension == ".doc";
        }

        private string SanitizeFileName(string fileName)
        {
            if (string.IsNullOrWhiteSpace(fileName))
                return "untitled";

            // 移除或替换Windows文件名中的非法字符
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = fileName;

            foreach (var invalidChar in invalidChars)
            {
                sanitized = sanitized.Replace(invalidChar, '_');
            }

            // 额外处理一些特殊字符
            sanitized = sanitized
                .Replace("\"", "_")  // 引号
                .Replace("'", "_")   // 单引号
                .Replace("\u201C", "_")   // 中文左引号
                .Replace("\u201D", "_")   // 中文右引号
                .Replace("\uFF08", "(")   // 中文左括号
                .Replace("\uFF09", ")")   // 中文右括号
                .Replace("  ", " ")   // 多个空格变为单个
                .Trim();

            // 限制文件名长度
            if (sanitized.Length > 100)
            {
                sanitized = sanitized.Substring(0, 100);
            }

            return sanitized;
        }
    }
}
