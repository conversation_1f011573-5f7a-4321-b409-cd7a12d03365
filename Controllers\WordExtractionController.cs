using Microsoft.AspNetCore.Mvc;
using WordExtractor.Models;
using WordExtractor.Services;

namespace WordExtractor.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class WordExtractionController : ControllerBase
    {
        private readonly IWordExtractionService _wordExtractionService;
        private readonly ILogger<WordExtractionController> _logger;

        public WordExtractionController(
            IWordExtractionService wordExtractionService,
            ILogger<WordExtractionController> logger)
        {
            _wordExtractionService = wordExtractionService;
            _logger = logger;
        }

        /// <summary>
        /// 分析Word文档，获取目录和标题信息
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <returns>文档分析结果</returns>
        [HttpPost("analyze")]
        public async Task<ActionResult<DocumentAnalysisResult>> AnalyzeDocument(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请选择一个有效的Word文档文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                using var stream = file.OpenReadStream();
                var result = await _wordExtractionService.AnalyzeDocumentAsync(stream);
                result.FileName = file.FileName;

                _logger.LogInformation($"成功分析文档: {file.FileName}, 段落数: {result.TotalParagraphs}, 标题数: {result.Headings.Count}");

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"分析文档时出错: {file?.FileName}");
                return StatusCode(500, $"分析文档时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取文档中的所有标题
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <returns>标题列表</returns>
        [HttpPost("headings")]
        public async Task<ActionResult<List<DocumentHeading>>> GetHeadings(IFormFile file)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请选择一个有效的Word文档文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                using var stream = file.OpenReadStream();
                var headings = await _wordExtractionService.GetDocumentHeadingsAsync(stream);

                _logger.LogInformation($"成功获取文档标题: {file.FileName}, 标题数: {headings.Count}");

                return Ok(headings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"获取文档标题时出错: {file?.FileName}");
                return StatusCode(500, $"获取文档标题时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 提取指定标题的内容，保留原格式
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <param name="request">提取请求参数</param>
        /// <returns>提取的Word文档</returns>
        [HttpPost("extract")]
        public async Task<IActionResult> ExtractSection(IFormFile file, [FromForm] ExtractionRequest request)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请选择一个有效的Word文档文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                if (string.IsNullOrWhiteSpace(request.TargetTitle))
                    return BadRequest("请指定要提取的标题");

                using var stream = file.OpenReadStream();
                var extractedBytes = await _wordExtractionService.ExtractSectionWithFormatAsync(stream, request.TargetTitle);

                var fileName = $"{request.TargetTitle.Replace("/", "_").Replace("\\", "_").Replace(":", "_")}.docx";

                _logger.LogInformation($"成功提取内容: {file.FileName} -> {fileName}");

                return File(extractedBytes, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"提取内容时出错: {file?.FileName}, 标题: {request?.TargetTitle}");
                return StatusCode(500, $"提取内容时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 批量提取多个标题的内容
        /// </summary>
        /// <param name="file">Word文档文件</param>
        /// <param name="titles">要提取的标题列表</param>
        /// <returns>提取结果列表</returns>
        [HttpPost("extract-batch")]
        public async Task<ActionResult<List<ExtractionResult>>> ExtractMultipleSections(
            IFormFile file, 
            [FromForm] string[] titles)
        {
            try
            {
                if (file == null || file.Length == 0)
                    return BadRequest("请选择一个有效的Word文档文件");

                if (!IsWordDocument(file.FileName))
                    return BadRequest("请上传Word文档文件 (.docx 或 .doc)");

                if (titles == null || titles.Length == 0)
                    return BadRequest("请指定要提取的标题列表");

                var results = new List<ExtractionResult>();

                foreach (var title in titles)
                {
                    try
                    {
                        using var stream = file.OpenReadStream();
                        var extractedBytes = await _wordExtractionService.ExtractSectionWithFormatAsync(stream, title);
                        
                        var fileName = $"{title.Replace("/", "_").Replace("\\", "_").Replace(":", "_")}.docx";
                        
                        results.Add(new ExtractionResult
                        {
                            Success = true,
                            Message = "提取成功",
                            FileName = fileName,
                            DocumentBytes = extractedBytes
                        });
                    }
                    catch (Exception ex)
                    {
                        results.Add(new ExtractionResult
                        {
                            Success = false,
                            Message = ex.Message,
                            FileName = title
                        });
                    }
                }

                _logger.LogInformation($"批量提取完成: {file.FileName}, 成功: {results.Count(r => r.Success)}/{results.Count}");

                return Ok(results);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"批量提取时出错: {file?.FileName}");
                return StatusCode(500, $"批量提取时出错: {ex.Message}");
            }
        }

        private static bool IsWordDocument(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension == ".docx" || extension == ".doc";
        }
    }
}
