{"GlobalPropertiesHash": "omhCvltmHlau8XaNYs3gpmo41ivhUdP9tM1AALJGRk8=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["OYB2T/603ij274SJsc0I5dzkDXrEHsJAIEx7/AkGZas=", "X9y2wOoERfsM2y0d8duH+N+kWktPQ07+OPYijwXwC/w=", "boZwjoD+lwpdWzXU0TmmBQd163ZAzE04GAtC5qQ+ecU=", "ImOBCc/vRyNbipNrcU9oxJu5KZBn5qISipfWOEHcFSU="], "CachedAssets": {"X9y2wOoERfsM2y0d8duH+N+kWktPQ07+OPYijwXwC/w=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f31xul4g0z", "Integrity": "NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 8604, "LastWriteTime": "2025-06-09T10:32:57.8328827+00:00"}, "OYB2T/603ij274SJsc0I5dzkDXrEHsJAIEx7/AkGZas=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v20ta0p5ky", "Integrity": "WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 21340, "LastWriteTime": "2025-06-09T09:06:09.7325042+00:00"}, "boZwjoD+lwpdWzXU0TmmBQd163ZAzE04GAtC5qQ+ecU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.html", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "template#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7q71xfk5fx", "Integrity": "X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template.html", "FileLength": 8650, "LastWriteTime": "2025-06-09T10:32:07.071837+00:00"}, "ImOBCc/vRyNbipNrcU9oxJu5KZBn5qISipfWOEHcFSU=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.js", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "template#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "g0zvhtklle", "Integrity": "AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template.js", "FileLength": 8614, "LastWriteTime": "2025-06-09T10:32:44.5755865+00:00"}}, "CachedCopyCandidates": {}}