{"GlobalPropertiesHash": "omhCvltmHlau8XaNYs3gpmo41ivhUdP9tM1AALJGRk8=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["OYB2T/603ij274SJsc0I5dzkDXrEHsJAIEx7/AkGZas=", "IYZ4uiH7mD+N0Zrr4kjYdeFSx1GlnuAizolbPByI5X4="], "CachedAssets": {"IYZ4uiH7mD+N0Zrr4kjYdeFSx1GlnuAizolbPByI5X4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h77qf1ra26", "Integrity": "AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 8359, "LastWriteTime": "2025-06-09T09:03:35.9531744+00:00"}, "OYB2T/603ij274SJsc0I5dzkDXrEHsJAIEx7/AkGZas=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v20ta0p5ky", "Integrity": "WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 21340, "LastWriteTime": "2025-06-09T09:06:09.7325042+00:00"}}, "CachedCopyCandidates": {}}