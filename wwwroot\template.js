// 模板替换功能的JavaScript代码

let templateFile = null;
let referenceFile = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeTemplateReplace();
});

function initializeTemplateReplace() {
    // 初始化文件上传区域
    setupFileUpload('templateUploadArea', 'templateFileInput', handleTemplateFileSelect);
    setupFileUpload('referenceUploadArea', 'referenceFileInput', handleReferenceFileSelect);
    
    // 初始化替换按钮
    document.getElementById('replaceBtn').addEventListener('click', executeReplace);
}

function setupFileUpload(uploadAreaId, fileInputId, handleFileSelect) {
    const uploadArea = document.getElementById(uploadAreaId);
    const fileInput = document.getElementById(fileInputId);
    
    // 点击上传区域
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // 文件选择
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽功能
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect({ target: { files: files } });
        }
    });
}

function handleTemplateFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        if (isWordDocument(file.name)) {
            templateFile = file;
            showFileInfo('templateUploadArea', 'templateFileInfo', 'templateFileName', file.name);
            updateStepStatus('step1', true);
            checkReadyToReplace();
        } else {
            showError('请选择Word文档文件 (.docx 或 .doc)');
        }
    }
}

function handleReferenceFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        if (isWordDocument(file.name)) {
            referenceFile = file;
            showFileInfo('referenceUploadArea', 'referenceFileInfo', 'referenceFileName', file.name);
            updateStepStatus('step2', true);
            checkReadyToReplace();
        } else {
            showError('请选择Word文档文件 (.docx 或 .doc)');
        }
    }
}

function showFileInfo(uploadAreaId, fileInfoId, fileNameId, fileName) {
    const uploadArea = document.getElementById(uploadAreaId);
    const fileInfo = document.getElementById(fileInfoId);
    const fileNameElement = document.getElementById(fileNameId);
    
    uploadArea.classList.add('has-file');
    fileInfo.style.display = 'block';
    fileNameElement.textContent = fileName;
}

function updateStepStatus(stepId, completed) {
    const step = document.getElementById(stepId);
    if (completed) {
        step.classList.add('step-completed');
        step.style.backgroundColor = '#d1e7dd';
        step.style.borderColor = '#198754';
    }
}

function checkReadyToReplace() {
    const replaceBtn = document.getElementById('replaceBtn');
    if (templateFile && referenceFile) {
        replaceBtn.disabled = false;
        updateStepStatus('step3', false);
    }
}

function isWordDocument(fileName) {
    const extension = fileName.toLowerCase().split('.').pop();
    return extension === 'docx' || extension === 'doc';
}

async function executeReplace() {
    if (!templateFile || !referenceFile) {
        showError('请先选择模板文档和参考文档');
        return;
    }
    
    const replaceBtn = document.getElementById('replaceBtn');
    const loading = document.querySelector('#step3 .loading');
    const errorAlert = document.getElementById('errorAlert');
    
    try {
        // 显示加载状态
        replaceBtn.disabled = true;
        loading.style.display = 'block';
        errorAlert.style.display = 'none';
        
        // 创建FormData
        const formData = new FormData();
        formData.append('templateFile', templateFile);
        formData.append('referenceFile', referenceFile);
        
        // 发送请求
        const response = await fetch('/api/wordextraction/replace-template', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let fileName = '替换结果.docx';
            if (contentDisposition) {
                const fileNameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                if (fileNameMatch) {
                    fileName = fileNameMatch[1].replace(/['"]/g, '');
                }
            }
            
            // 下载文件
            const blob = await response.blob();
            downloadFile(blob, fileName);
            
            // 显示成功结果
            showSuccess(fileName);
            updateStepStatus('step3', true);
            
        } else {
            const errorText = await response.text();
            throw new Error(errorText || '模板替换失败');
        }
        
    } catch (error) {
        console.error('模板替换错误:', error);
        showError(error.message || '模板替换过程中发生错误');
    } finally {
        // 隐藏加载状态
        loading.style.display = 'none';
        replaceBtn.disabled = false;
    }
}

function downloadFile(blob, fileName) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

function showSuccess(fileName) {
    const resultSection = document.getElementById('resultSection');
    const resultInfo = document.getElementById('resultInfo');
    
    resultInfo.innerHTML = `
        <div class="d-flex align-items-center justify-content-between">
            <div>
                <h6><i class="fas fa-file-download text-success"></i> 文件已下载</h6>
                <p class="mb-0">文件名: <strong>${fileName}</strong></p>
                <p class="text-muted small mb-0">模板中的标签已被替换为参考文档中的对应内容</p>
            </div>
            <div>
                <button class="btn btn-primary" onclick="resetForm()">
                    <i class="fas fa-redo"></i> 重新开始
                </button>
            </div>
        </div>
    `;
    
    resultSection.style.display = 'block';
    
    // 滚动到结果区域
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

function showError(message) {
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');
    
    errorMessage.textContent = message;
    errorAlert.style.display = 'block';
    
    // 滚动到错误信息
    errorAlert.scrollIntoView({ behavior: 'smooth' });
    
    // 3秒后自动隐藏错误信息
    setTimeout(() => {
        errorAlert.style.display = 'none';
    }, 5000);
}

function resetForm() {
    // 重置文件
    templateFile = null;
    referenceFile = null;
    
    // 重置文件输入
    document.getElementById('templateFileInput').value = '';
    document.getElementById('referenceFileInput').value = '';
    
    // 重置上传区域
    resetUploadArea('templateUploadArea', 'templateFileInfo');
    resetUploadArea('referenceUploadArea', 'referenceFileInfo');
    
    // 重置步骤状态
    resetStepStatus('step1');
    resetStepStatus('step2');
    resetStepStatus('step3');
    
    // 重置按钮和结果
    document.getElementById('replaceBtn').disabled = true;
    document.getElementById('resultSection').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
    
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' });
}

function resetUploadArea(uploadAreaId, fileInfoId) {
    const uploadArea = document.getElementById(uploadAreaId);
    const fileInfo = document.getElementById(fileInfoId);
    
    uploadArea.classList.remove('has-file');
    fileInfo.style.display = 'none';
}

function resetStepStatus(stepId) {
    const step = document.getElementById(stepId);
    step.classList.remove('step-completed');
    step.style.backgroundColor = '';
    step.style.borderColor = '';
}
