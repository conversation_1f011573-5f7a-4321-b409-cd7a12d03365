# Word文档内容提取工具

这是一个基于C# ASP.NET Core的Web应用程序，用于分析Word文档并提取指定标题的内容，完全保留原始格式。

## 功能特性

- 📄 **文档分析**: 自动分析Word文档结构，提取目录和标题信息
- 🎯 **精确提取**: 根据标题名称精确提取指定章节内容
- 🎨 **格式保留**: 完全保留原始文档的格式、样式、表格和布局
- 🌐 **Web界面**: 友好的Web用户界面，支持拖拽上传
- 📊 **批量处理**: 支持批量提取多个章节
- 🔍 **智能识别**: 自动识别目录结构和标题层级

## 技术栈

- **后端**: ASP.NET Core 8.0
- **文档处理**: DocumentFormat.OpenXml
- **前端**: HTML5 + Bootstrap 5 + JavaScript
- **API文档**: Swagger/OpenAPI

## 系统要求

- .NET 8.0 SDK
- Windows 10/11 或 Windows Server 2019+
- 支持的文档格式: .docx, .doc

## 快速开始

### 1. 安装.NET 8 SDK

从 [Microsoft官网](https://dotnet.microsoft.com/download/dotnet/8.0) 下载并安装.NET 8 SDK。

### 2. 运行应用程序

双击 `run.bat` 文件，或在命令行中执行：

```bash
dotnet restore
dotnet build
dotnet run
```

### 3. 访问应用程序

打开浏览器访问：
- HTTP: http://localhost:5000
- HTTPS: https://localhost:5001

## 使用说明

### 1. 上传文档
- 点击上传区域选择Word文档，或直接拖拽文件到上传区域
- 支持 .docx 和 .doc 格式

### 2. 分析文档
- 点击"分析文档"按钮
- 系统会自动分析文档结构，提取目录和标题信息

### 3. 选择内容
- 在目录结构中点击要提取的标题
- 支持多级标题选择

### 4. 提取内容
- 点击"提取选中内容"按钮
- 系统会生成新的Word文档并自动下载

## API接口

### 分析文档
```
POST /api/wordextraction/analyze
Content-Type: multipart/form-data
Body: file (Word文档)
```

### 获取标题列表
```
POST /api/wordextraction/headings
Content-Type: multipart/form-data
Body: file (Word文档)
```

### 提取指定内容
```
POST /api/wordextraction/extract
Content-Type: multipart/form-data
Body: 
  - file (Word文档)
  - TargetTitle (目标标题)
  - PreserveFormatting (是否保留格式，默认true)
  - IncludeTables (是否包含表格，默认true)
  - IncludeImages (是否包含图片，默认true)
```

### 批量提取
```
POST /api/wordextraction/extract-batch
Content-Type: multipart/form-data
Body:
  - file (Word文档)
  - titles[] (标题数组)
```

## 项目结构

```
WordExtractor/
├── Controllers/
│   └── WordExtractionController.cs    # API控制器
├── Services/
│   ├── IWordExtractionService.cs      # 服务接口
│   └── WordExtractionService.cs       # 核心服务实现
├── Models/
│   └── DocumentModels.cs              # 数据模型
├── wwwroot/
│   ├── index.html                     # 主页面
│   └── app.js                         # 前端脚本
├── Program.cs                         # 应用程序入口
├── WordExtractor.csproj               # 项目文件
├── run.bat                           # 启动脚本
└── README.md                         # 说明文档
```

## 核心特性说明

### 格式保留机制

本工具使用 DocumentFormat.OpenXml 库的深度克隆功能，确保：

1. **样式保留**: 完全保留字体、颜色、大小等样式信息
2. **表格格式**: 保留表格的边框、对齐、合并单元格等格式
3. **段落格式**: 保留缩进、对齐、行距等段落格式
4. **文档结构**: 保留标题层级和文档结构

### 智能内容识别

- 自动识别Word文档的目录结构
- 支持多种目录格式（toc样式、手动目录等）
- 智能判断章节边界
- 准确提取相关表格和内容

## 故障排除

### 常见问题

1. **文档无法上传**
   - 检查文件格式是否为 .docx 或 .doc
   - 确认文件大小不超过100MB

2. **分析失败**
   - 确认文档没有密码保护
   - 检查文档是否损坏

3. **提取内容为空**
   - 确认标题名称完全匹配
   - 检查文档是否包含指定标题

4. **格式丢失**
   - 确认使用的是 .docx 格式（.doc格式可能有兼容性问题）
   - 检查原文档的样式定义

## 开发说明

### 添加新功能

1. 在 `IWordExtractionService` 中定义接口
2. 在 `WordExtractionService` 中实现功能
3. 在 `WordExtractionController` 中添加API端点
4. 更新前端界面和脚本

### 自定义配置

可以在 `Program.cs` 中修改：
- 文件大小限制
- 端口配置
- 日志级别

## 许可证

本项目采用 MIT 许可证。

## 支持

如有问题或建议，请提交 Issue 或联系开发团队。
