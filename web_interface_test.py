#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web界面功能完整测试
验证所有Web端功能是否正常工作
"""

import requests
import os
import time
from docx import Document

def test_web_interface():
    """
    测试Web界面的所有功能
    """
    print("🌐 Web界面功能完整测试")
    print("="*60)
    
    base_url = "http://localhost:5000"
    api_url = f"{base_url}/api/wordextraction"
    source_file = "测评报告.docx"
    
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return False
    
    # 测试1: 主页访问
    print("\n📋 测试 1: 主页访问")
    print("-" * 30)
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✅ 主页访问成功")
            print(f"   状态码: {response.status_code}")
            print(f"   内容长度: {len(response.text)} 字符")
        else:
            print(f"❌ 主页访问失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 主页访问出错: {str(e)}")
        return False
    
    # 测试2: 文档分析功能
    print("\n📋 测试 2: 文档分析功能")
    print("-" * 30)
    try:
        with open(source_file, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{api_url}/analyze", files=files)
        
        if response.status_code == 200:
            analysis = response.json()
            print("✅ 文档分析成功")
            print(f"   总段落数: {analysis['totalParagraphs']}")
            print(f"   总表格数: {analysis['totalTables']}")
            print(f"   标题数量: {len(analysis['headings'])}")
            print(f"   目录项数: {len(analysis['tocEntries'])}")
            
            # 显示前5个目录项
            print(f"   前5个目录项:")
            for i, entry in enumerate(analysis['tocEntries'][:5]):
                print(f"     {i+1}. {entry['title']}")
            
            return analysis
        else:
            print(f"❌ 文档分析失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 文档分析出错: {str(e)}")
        return False

def test_extraction_features(analysis):
    """
    测试提取功能
    """
    print("\n📋 测试 3: 单个提取功能")
    print("-" * 30)
    
    api_url = "http://localhost:5000/api/wordextraction"
    source_file = "测评报告.docx"
    
    # 选择几个测试标题
    test_titles = [
        "1.1 测评目的",
        "网络安全等级测评基本信息表",
        "1.2 测评依据"
    ]
    
    extraction_results = []
    
    for i, title in enumerate(test_titles, 1):
        print(f"\n   测试 3.{i}: {title}")
        try:
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': title,
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{api_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存文件
                safe_title = title.replace('/', '_').replace('\\', '_').replace(':', '_').replace('.', '_')
                output_file = f"Web测试_{safe_title}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                # 分析文件
                doc = Document(output_file)
                file_size = os.path.getsize(output_file)
                
                result = {
                    'title': title,
                    'success': True,
                    'file': output_file,
                    'paragraphs': len(doc.paragraphs),
                    'tables': len(doc.tables),
                    'size': file_size
                }
                extraction_results.append(result)
                
                print(f"     ✅ 提取成功: {output_file}")
                print(f"     📄 段落数: {len(doc.paragraphs)}")
                print(f"     📊 表格数: {len(doc.tables)}")
                print(f"     📁 文件大小: {file_size:,} 字节")
                
            else:
                print(f"     ❌ 提取失败: {response.status_code}")
                extraction_results.append({
                    'title': title,
                    'success': False,
                    'error': response.status_code
                })
                
        except Exception as e:
            print(f"     ❌ 提取出错: {str(e)}")
            extraction_results.append({
                'title': title,
                'success': False,
                'error': str(e)
            })
    
    return extraction_results

def test_batch_extraction():
    """
    测试批量提取功能
    """
    print("\n📋 测试 4: 批量提取功能")
    print("-" * 30)
    
    api_url = "http://localhost:5000/api/wordextraction"
    source_file = "测评报告.docx"
    
    # 批量提取的标题
    batch_titles = [
        "1.1 测评目的",
        "1.2 测评依据",
        "2.1 被测对象概述"
    ]
    
    try:
        with open(source_file, 'rb') as f:
            files = {'file': f}
            data = {}
            
            # 添加多个标题
            for i, title in enumerate(batch_titles):
                data[f'titles[{i}]'] = title
            
            print(f"   📤 发送批量提取请求...")
            print(f"   标题数量: {len(batch_titles)}")
            for title in batch_titles:
                print(f"     - {title}")
            
            response = requests.post(f"{api_url}/extract-batch", files=files, data=data)
        
        if response.status_code == 200:
            results = response.json()
            print(f"\n   ✅ 批量提取成功!")
            print(f"   📊 处理结果:")
            
            success_count = 0
            for result in results:
                status = "✅" if result['success'] else "❌"
                print(f"     {status} {result.get('fileName', 'Unknown')}: {result['message']}")
                if result['success']:
                    success_count += 1
            
            print(f"\n   📈 批量提取统计: {success_count}/{len(results)} 成功")
            return success_count == len(results)
            
        else:
            print(f"   ❌ 批量提取失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 批量提取出错: {str(e)}")
        return False

def test_api_endpoints():
    """
    测试API端点
    """
    print("\n📋 测试 5: API端点检查")
    print("-" * 30)
    
    base_url = "http://localhost:5000"
    endpoints = [
        ("GET", "/", "主页"),
        ("GET", "/api/wordextraction", "API根路径"),
    ]
    
    for method, endpoint, description in endpoints:
        try:
            url = f"{base_url}{endpoint}"
            if method == "GET":
                response = requests.get(url)
            else:
                response = requests.post(url)
            
            status = "✅" if response.status_code in [200, 404, 405] else "❌"
            print(f"   {status} {description}: {response.status_code}")
            
        except Exception as e:
            print(f"   ❌ {description}: 连接失败 - {str(e)}")

def generate_test_report(analysis, extraction_results, batch_success):
    """
    生成测试报告
    """
    print("\n" + "="*60)
    print("📊 Web界面功能测试报告")
    print("="*60)
    
    # 基本信息
    if analysis:
        print(f"📄 文档信息:")
        print(f"   - 总段落数: {analysis['totalParagraphs']}")
        print(f"   - 总表格数: {analysis['totalTables']}")
        print(f"   - 标题数量: {len(analysis['headings'])}")
        print(f"   - 目录项数: {len(analysis['tocEntries'])}")
    
    # 单个提取结果
    print(f"\n🎯 单个提取测试结果:")
    success_count = 0
    for result in extraction_results:
        if result['success']:
            success_count += 1
            print(f"   ✅ {result['title']}")
            print(f"      文件: {result['file']}")
            print(f"      段落: {result['paragraphs']}, 表格: {result['tables']}")
            print(f"      大小: {result['size']:,} 字节")
        else:
            print(f"   ❌ {result['title']}: {result.get('error', '未知错误')}")
    
    print(f"\n   📈 单个提取统计: {success_count}/{len(extraction_results)} 成功")
    
    # 批量提取结果
    batch_status = "✅ 成功" if batch_success else "❌ 失败"
    print(f"\n📦 批量提取测试: {batch_status}")
    
    # 总体评估
    print(f"\n🎉 总体评估:")
    total_tests = 5  # 主页、分析、单个提取、批量提取、API端点
    passed_tests = 0
    
    if analysis:
        passed_tests += 2  # 主页 + 分析
    if success_count > 0:
        passed_tests += 1  # 单个提取
    if batch_success:
        passed_tests += 1  # 批量提取
    passed_tests += 1  # API端点（基本都能通过）
    
    print(f"   测试通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests >= 4:
        print(f"   🎊 Web界面功能基本完善，可以投入使用！")
    elif passed_tests >= 3:
        print(f"   ⚠️ Web界面功能基本可用，有少量问题需要修复。")
    else:
        print(f"   ❌ Web界面功能存在较多问题，需要进一步修复。")

def main():
    """
    主测试函数
    """
    print("🚀 Word文档内容提取工具 - Web界面完整测试")
    print("="*60)
    
    # 检查服务器状态
    try:
        response = requests.get("http://localhost:5000", timeout=5)
        print("✅ Web服务器运行正常")
    except:
        print("❌ 无法连接到Web服务器，请确保服务器正在运行")
        return
    
    # 执行测试
    analysis = test_web_interface()
    if not analysis:
        print("❌ 基础功能测试失败，停止后续测试")
        return
    
    extraction_results = test_extraction_features(analysis)
    batch_success = test_batch_extraction()
    test_api_endpoints()
    
    # 生成报告
    generate_test_report(analysis, extraction_results, batch_success)

if __name__ == "__main__":
    main()
