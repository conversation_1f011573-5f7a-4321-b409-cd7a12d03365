#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证修复后的提取功能
确保提取的是实际内容而不是目录
"""

import requests
import os
from docx import Document

def verify_extraction_fix():
    """
    验证提取修复
    """
    print("🔧 验证修复后的提取功能")
    print("="*50)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return False
    
    # 测试标题列表
    test_cases = [
        {
            "title": "1.1 测评目的",
            "expected_content": "应该包含测评目的的具体描述，而不是目录项",
            "min_length": 100  # 期望的最小内容长度
        },
        {
            "title": "1.2 测评依据",
            "expected_content": "应该包含测评依据的详细内容",
            "min_length": 200
        },
        {
            "title": "2.1 被测对象概述",
            "expected_content": "应该包含被测对象的详细概述",
            "min_length": 300
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['title']}")
        print("-" * 40)
        
        try:
            # 调用API提取
            base_url = "http://localhost:5000/api/wordextraction"
            
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': test_case['title'],
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存提取的文档
                output_file = f"修复验证_{test_case['title'].replace('.', '_').replace(' ', '_')}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                # 分析提取的内容
                doc = Document(output_file)
                
                print(f"✅ 提取成功: {output_file}")
                print(f"📄 段落数: {len(doc.paragraphs)}")
                print(f"📊 表格数: {len(doc.tables)}")
                print(f"📁 文件大小: {os.path.getsize(output_file):,} 字节")
                
                # 获取所有文本内容
                all_text = ""
                for para in doc.paragraphs:
                    text = para.text.strip()
                    if text:
                        all_text += text + " "
                
                print(f"📝 内容长度: {len(all_text)} 字符")
                
                # 显示前200个字符的内容
                preview = all_text[:200] + ("..." if len(all_text) > 200 else "")
                print(f"📖 内容预览: {preview}")
                
                # 验证内容质量
                is_valid = True
                issues = []
                
                # 检查1: 内容长度
                if len(all_text) < test_case['min_length']:
                    is_valid = False
                    issues.append(f"内容太短 ({len(all_text)} < {test_case['min_length']})")
                
                # 检查2: 是否只是目录项
                if is_only_toc_content(all_text):
                    is_valid = False
                    issues.append("内容疑似只是目录项")
                
                # 检查3: 是否包含实际描述性内容
                if not has_descriptive_content(all_text):
                    is_valid = False
                    issues.append("缺少描述性内容")
                
                if is_valid:
                    print(f"✅ 验证通过: 内容质量良好")
                    success_count += 1
                else:
                    print(f"❌ 验证失败:")
                    for issue in issues:
                        print(f"   - {issue}")
                
            else:
                print(f"❌ 提取失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
    
    print(f"\n📊 验证结果: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)

def is_only_toc_content(text):
    """
    判断是否只是目录内容
    """
    # 如果文本很短且包含很多数字，可能是目录
    if len(text) < 100:
        digit_count = sum(1 for c in text if c.isdigit())
        if digit_count > len(text) * 0.3:  # 数字占比超过30%
            return True
    
    # 检查是否包含典型的目录模式
    toc_patterns = [
        r'\d+\s*$',  # 以数字结尾
        r'\.\.\.',   # 包含省略号
        r'第\s*\d+\s*页',  # 包含页码
    ]
    
    import re
    for pattern in toc_patterns:
        if re.search(pattern, text):
            return True
    
    return False

def has_descriptive_content(text):
    """
    判断是否包含描述性内容
    """
    # 检查是否包含常见的描述性词汇
    descriptive_words = [
        '为了', '通过', '根据', '按照', '依据', '目的', '目标', 
        '包括', '涉及', '主要', '具体', '详细', '说明', '描述',
        '分析', '评估', '检查', '验证', '确保', '保证'
    ]
    
    word_count = sum(1 for word in descriptive_words if word in text)
    return word_count >= 2  # 至少包含2个描述性词汇

def compare_with_source():
    """
    与源文档对比，验证提取的准确性
    """
    print(f"\n🔍 与源文档对比验证")
    print("="*50)
    
    source_file = "测评报告.docx"
    try:
        doc = Document(source_file)
        
        # 查找"1.1 测评目的"在源文档中的实际位置
        target_title = "1.1 测评目的"
        
        print(f"🔍 在源文档中查找 '{target_title}':")
        
        found_positions = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            style_name = para.style.name if para.style else "Normal"
            
            if target_title in text:
                found_positions.append({
                    'index': i,
                    'text': text,
                    'style': style_name,
                    'is_toc': 'toc' in style_name.lower()
                })
        
        for pos in found_positions:
            toc_marker = " (目录)" if pos['is_toc'] else " (正文)"
            print(f"   段落 {pos['index']}: {pos['text']} - 样式: {pos['style']}{toc_marker}")
        
        # 找到正文中的位置
        content_position = None
        for pos in found_positions:
            if not pos['is_toc']:
                content_position = pos
                break
        
        if content_position:
            print(f"\n📄 正文标题位置: 段落 {content_position['index']}")
            
            # 显示该位置后的内容
            start_idx = content_position['index']
            end_idx = min(start_idx + 10, len(doc.paragraphs))
            
            print(f"📖 正文标题后的内容:")
            for i in range(start_idx, end_idx):
                para = doc.paragraphs[i]
                text = para.text.strip()
                if text:
                    print(f"   段落 {i}: {text[:100]}{'...' if len(text) > 100 else ''}")
        
    except Exception as e:
        print(f"❌ 对比验证时出错: {str(e)}")

def main():
    """
    主验证函数
    """
    print("🔧 修复后的提取功能验证")
    print("="*60)
    
    # 验证修复效果
    extraction_fixed = verify_extraction_fix()
    
    # 与源文档对比
    compare_with_source()
    
    print(f"\n{'='*60}")
    print("验证总结")
    print('='*60)
    
    if extraction_fixed:
        print("🎉 修复验证成功！提取功能现在能够正确提取实际内容。")
        print("✅ 不再提取目录项，而是提取真正的章节内容。")
    else:
        print("⚠️ 修复验证失败，需要进一步调整。")
        print("💡 建议检查标题匹配逻辑和内容边界检测。")

if __name__ == "__main__":
    main()
