#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试模板文档
用于验证模板替换功能
"""

from docx import Document
from docx.shared import Inches

def create_test_template():
    """
    创建包含标签的测试模板文档
    """
    print("📝 创建测试模板文档")
    
    # 创建新文档
    doc = Document()
    
    # 添加标题
    title = doc.add_heading('测评报告模板', 0)
    
    # 添加说明段落
    doc.add_paragraph('本文档是一个测试模板，包含了各种标签，用于验证模板替换功能。')
    
    # 添加基本信息表标签
    doc.add_heading('基本信息', level=1)
    doc.add_paragraph('以下是网络安全等级测评的基本信息：')
    doc.add_paragraph('{网络安全等级测评基本信息表}')
    
    # 添加项目概述标签
    doc.add_heading('项目概述', level=1)
    doc.add_paragraph('本次测评项目的概述如下：')
    doc.add_paragraph('{测评项目概述}')
    
    # 添加测评目的标签
    doc.add_heading('测评目的', level=1)
    doc.add_paragraph('本次测评的具体目的：')
    doc.add_paragraph('{测评项目概述-测评目的}')
    
    # 添加测评依据标签
    doc.add_heading('测评依据', level=1)
    doc.add_paragraph('本次测评所依据的标准和规范：')
    doc.add_paragraph('{测评项目概述-测评依据}')
    
    # 添加声明标签
    doc.add_heading('声明', level=1)
    doc.add_paragraph('测评机构声明：')
    doc.add_paragraph('{声明}')
    
    # 添加测评结论标签
    doc.add_heading('测评结论', level=1)
    doc.add_paragraph('经过全面测评，得出以下结论：')
    doc.add_paragraph('{等级测评结论}')
    
    # 添加风险隐患标签
    doc.add_heading('风险隐患', level=1)
    doc.add_paragraph('测评发现的重大风险隐患及整改建议：')
    doc.add_paragraph('{重大风险隐患及整改建议}')
    
    # 添加混合内容示例
    doc.add_heading('混合内容示例', level=1)
    doc.add_paragraph('这是一个包含标签的段落：根据{测评项目概述-测评目的}，我们进行了全面的安全测评。')
    doc.add_paragraph('测评结果显示：{等级测评结论}')
    
    # 添加表格示例
    doc.add_heading('表格示例', level=1)
    doc.add_paragraph('以下表格将被替换为实际内容：')
    
    table = doc.add_table(rows=3, cols=2)
    table.style = 'Table Grid'
    
    # 表格标题行
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = '项目'
    hdr_cells[1].text = '内容'
    
    # 表格内容行
    row1_cells = table.rows[1].cells
    row1_cells[0].text = '测评目的'
    row1_cells[1].text = '{测评项目概述-测评目的}'
    
    row2_cells = table.rows[2].cells
    row2_cells[0].text = '测评依据'
    row2_cells[1].text = '{测评项目概述-测评依据}'
    
    # 添加结尾
    doc.add_paragraph('')
    doc.add_paragraph('注意：上述标签将被自动替换为参考文档中的对应内容。')
    
    # 保存文档
    filename = '测试模板.docx'
    doc.save(filename)
    
    print(f"✅ 测试模板已创建: {filename}")
    
    # 显示模板中的标签
    print(f"\n📋 模板中包含的标签:")
    tags = [
        '{网络安全等级测评基本信息表}',
        '{测评项目概述}',
        '{测评项目概述-测评目的}',
        '{测评项目概述-测评依据}',
        '{声明}',
        '{等级测评结论}',
        '{重大风险隐患及整改建议}'
    ]
    
    for i, tag in enumerate(tags, 1):
        print(f"   {i}. {tag}")
    
    print(f"\n💡 使用说明:")
    print(f"1. 将此模板文档上传到模板替换工具")
    print(f"2. 将'测评报告.docx'作为参考文档上传")
    print(f"3. 点击'开始替换'按钮")
    print(f"4. 下载替换后的文档查看结果")

def create_simple_template():
    """
    创建简单的测试模板
    """
    print(f"\n📝 创建简单测试模板")
    
    doc = Document()
    
    # 添加标题
    doc.add_heading('简单模板测试', 0)
    
    # 添加几个基本标签
    doc.add_paragraph('测评目的：{测评项目概述-测评目的}')
    doc.add_paragraph('')
    doc.add_paragraph('测评依据：{测评项目概述-测评依据}')
    doc.add_paragraph('')
    doc.add_paragraph('声明内容：{声明}')
    
    # 保存文档
    filename = '简单模板.docx'
    doc.save(filename)
    
    print(f"✅ 简单模板已创建: {filename}")

def main():
    """
    主函数
    """
    print("🔧 创建模板替换功能测试文档")
    print("="*50)
    
    # 创建详细的测试模板
    create_test_template()
    
    # 创建简单的测试模板
    create_simple_template()
    
    print(f"\n🎉 测试模板创建完成！")
    print(f"现在可以使用这些模板测试模板替换功能了。")

if __name__ == "__main__":
    main()
