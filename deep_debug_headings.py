#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试标题匹配问题
找出正文中的实际标题位置和格式
"""

import os
from docx import Document

def deep_analyze_headings():
    """
    深度分析文档中的标题
    """
    print("🔍 深度分析文档标题")
    print("="*50)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return
    
    try:
        doc = Document(source_file)
        
        # 查找所有包含"测评目的"的段落
        target_keywords = ["测评目的", "测评依据", "被测对象概述"]
        
        for keyword in target_keywords:
            print(f"\n🎯 查找包含 '{keyword}' 的所有段落:")
            print("-" * 40)
            
            found_count = 0
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                style_name = para.style.name if para.style else "Normal"
                
                if keyword in text:
                    found_count += 1
                    is_toc = 'toc' in style_name.lower()
                    is_heading = 'heading' in style_name.lower()
                    
                    print(f"段落 {i:3d}: {text}")
                    print(f"         样式: {style_name}")
                    print(f"         类型: {'目录' if is_toc else ('标题' if is_heading else '普通')}")
                    
                    # 如果不是目录，显示周围的内容
                    if not is_toc:
                        print(f"         周围内容:")
                        start_idx = max(0, i - 2)
                        end_idx = min(len(doc.paragraphs), i + 5)
                        
                        for j in range(start_idx, end_idx):
                            if j == i:
                                continue
                            nearby_text = doc.paragraphs[j].text.strip()
                            nearby_style = doc.paragraphs[j].style.name if doc.paragraphs[j].style else "Normal"
                            if nearby_text:
                                marker = " <-- 当前" if j == i else ""
                                print(f"           段落 {j}: {nearby_text[:50]}{'...' if len(nearby_text) > 50 else ''} ({nearby_style}){marker}")
                    
                    print()
            
            print(f"总共找到 {found_count} 个包含 '{keyword}' 的段落")

    except Exception as e:
        print(f"❌ 深度分析时出错: {str(e)}")

def analyze_heading_styles():
    """
    分析文档中的所有标题样式
    """
    print(f"\n📊 分析文档中的标题样式")
    print("="*50)
    
    source_file = "测评报告.docx"
    try:
        doc = Document(source_file)
        
        # 统计所有样式
        style_counts = {}
        heading_styles = {}
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            style_name = para.style.name if para.style else "Normal"
            
            # 统计样式使用次数
            style_counts[style_name] = style_counts.get(style_name, 0) + 1
            
            # 收集标题样式的示例
            if ('heading' in style_name.lower() or 
                style_name.startswith('标题') or
                (text and len(text) < 100 and any(char.isdigit() for char in text[:10]))):
                
                if style_name not in heading_styles:
                    heading_styles[style_name] = []
                
                if len(heading_styles[style_name]) < 5:  # 只保存前5个示例
                    heading_styles[style_name].append({
                        'index': i,
                        'text': text,
                        'is_toc': 'toc' in style_name.lower()
                    })
        
        # 显示标题样式
        print("🏷️ 发现的标题样式:")
        for style_name, examples in heading_styles.items():
            print(f"\n样式: {style_name} (使用次数: {style_counts[style_name]})")
            for example in examples:
                toc_marker = " [目录]" if example['is_toc'] else " [正文]"
                print(f"   段落 {example['index']}: {example['text']}{toc_marker}")
        
        # 显示最常用的样式
        print(f"\n📈 最常用的样式 (前10个):")
        sorted_styles = sorted(style_counts.items(), key=lambda x: x[1], reverse=True)
        for style_name, count in sorted_styles[:10]:
            print(f"   {style_name}: {count} 次")
            
    except Exception as e:
        print(f"❌ 分析样式时出错: {str(e)}")

def find_actual_content_headings():
    """
    查找实际的正文标题
    """
    print(f"\n🎯 查找实际的正文标题")
    print("="*50)
    
    source_file = "测评报告.docx"
    try:
        doc = Document(source_file)
        
        # 查找可能的正文标题模式
        potential_headings = []
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            style_name = para.style.name if para.style else "Normal"
            
            # 跳过目录样式
            if 'toc' in style_name.lower():
                continue
            
            # 查找符合标题模式的段落
            import re
            heading_patterns = [
                r'^\d+\s+\w+',           # 数字开头的标题 (如 "1 测评项目概述")
                r'^\d+\.\d+\s+\w+',      # 数字.数字开头 (如 "1.1 测评目的")
                r'^\d+\.\d+\.\d+\s+\w+', # 三级编号 (如 "1.1.1 具体内容")
            ]
            
            for pattern in heading_patterns:
                if re.match(pattern, text):
                    potential_headings.append({
                        'index': i,
                        'text': text,
                        'style': style_name,
                        'pattern': pattern
                    })
                    break
        
        print(f"🔍 找到 {len(potential_headings)} 个潜在的正文标题:")
        
        # 按索引排序并显示
        potential_headings.sort(key=lambda x: x['index'])
        
        for heading in potential_headings[:20]:  # 只显示前20个
            print(f"段落 {heading['index']:3d}: {heading['text']} (样式: {heading['style']})")
        
        if len(potential_headings) > 20:
            print(f"... 还有 {len(potential_headings) - 20} 个标题")
            
    except Exception as e:
        print(f"❌ 查找正文标题时出错: {str(e)}")

def main():
    """
    主调试函数
    """
    print("🐛 深度调试标题匹配问题")
    print("="*60)
    
    # 深度分析标题
    deep_analyze_headings()
    
    # 分析标题样式
    analyze_heading_styles()
    
    # 查找实际的正文标题
    find_actual_content_headings()
    
    print(f"\n💡 调试结论:")
    print(f"1. 检查是否存在正文中的标题")
    print(f"2. 确认标题的实际样式和格式")
    print(f"3. 调整匹配逻辑以适应实际的标题格式")

if __name__ == "__main__":
    main()
