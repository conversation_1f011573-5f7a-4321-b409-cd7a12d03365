#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试用户反馈的具体问题
检查是否有特定标题存在内容过多的问题
"""

import requests
import os
from docx import Document

def test_user_reported_issue():
    """
    测试用户反馈的具体问题
    """
    print("🔍 测试用户反馈的内容过多问题")
    print("="*60)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return
    
    # 测试一些可能有问题的标题
    test_cases = [
        {
            "title": "测评项目概述",
            "description": "可能包含过多概述内容"
        },
        {
            "title": "被测对象描述", 
            "description": "可能包含过多描述内容"
        },
        {
            "title": "2.1 被测对象概述",
            "description": "可能包含其他2.x章节内容"
        },
        {
            "title": "网络安全等级测评基本信息表",
            "description": "可能包含过多表格内容"
        },
        {
            "title": "重大风险隐患及整改建议",
            "description": "可能包含过多建议内容"
        }
    ]
    
    base_url = "http://localhost:5000/api/wordextraction"
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['title']}")
        print(f"   问题描述: {test_case['description']}")
        print("-" * 50)
        
        try:
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': test_case['title'],
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存文件
                safe_title = test_case['title'].replace('/', '_').replace('\\', '_').replace(':', '_').replace('.', '_')
                output_file = f"用户问题测试_{safe_title}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                # 详细分析内容
                doc = Document(output_file)
                file_size = os.path.getsize(output_file)
                
                print(f"   ✅ 提取成功: {output_file}")
                print(f"   📄 段落数: {len(doc.paragraphs)}")
                print(f"   📊 表格数: {len(doc.tables)}")
                print(f"   📁 文件大小: {file_size:,} 字节")
                
                # 分析所有段落内容
                print(f"\n   📄 详细段落分析:")
                for j, para in enumerate(doc.paragraphs):
                    text = para.text.strip()
                    if text:
                        # 检查是否可能是其他标题或不相关内容
                        is_title_like = (
                            any(keyword in text for keyword in ["1.", "2.", "3.", "4.", "5."]) or
                            any(keyword in text for keyword in ["测评", "安全", "管理", "技术"]) and len(text) < 50
                        )
                        
                        marker = " ⚠️ 可能是其他标题" if is_title_like and j > 0 else ""
                        print(f"     段落 {j+1:2d}: {text[:100]}{'...' if len(text) > 100 else ''}{marker}")
                
                # 分析表格内容
                if doc.tables:
                    print(f"\n   📊 表格内容分析:")
                    for j, table in enumerate(doc.tables):
                        print(f"     表格 {j+1}: {len(table.rows)} 行 x {len(table.columns)} 列")
                        
                        # 显示表格前几行内容
                        for row_idx, row in enumerate(table.rows[:3]):
                            row_text = []
                            for cell in row.cells:
                                cell_text = cell.text.strip()
                                if cell_text:
                                    row_text.append(cell_text[:30] + ('...' if len(cell_text) > 30 else ''))
                            if row_text:
                                print(f"       行 {row_idx+1}: {' | '.join(row_text)}")
                        
                        if len(table.rows) > 3:
                            print(f"       ... (还有 {len(table.rows) - 3} 行)")
                
                # 评估内容是否过多
                all_text = " ".join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
                
                if len(all_text) > 2000:
                    print(f"\n   ⚠️ 内容可能过多: {len(all_text)} 字符")
                    print(f"   💡 建议: 可能需要缩小提取范围")
                elif len(all_text) > 1000:
                    print(f"\n   ⚠️ 内容较多: {len(all_text)} 字符")
                    print(f"   💡 建议: 内容量适中，但可以考虑优化")
                else:
                    print(f"\n   ✅ 内容量合适: {len(all_text)} 字符")
                
            else:
                print(f"   ❌ 提取失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 测试出错: {str(e)}")

def suggest_content_optimization():
    """
    建议内容优化方案
    """
    print(f"\n💡 内容优化建议")
    print("="*50)
    
    print(f"基于测试结果，如果用户认为内容过多，可以考虑以下优化:")
    print(f"")
    print(f"1. **精简模式**: 只提取标题和第一段内容")
    print(f"2. **表格分离**: 将表格内容单独提取，不包含在正文中")
    print(f"3. **范围限制**: 进一步缩小提取范围，只包含最相关的内容")
    print(f"4. **用户选择**: 提供不同的提取模式供用户选择")
    print(f"")
    print(f"当前提取策略:")
    print(f"- ✅ 边界检测准确，不会跨越到其他标题")
    print(f"- ✅ 格式保留完整，包含所有相关表格")
    print(f"- ⚠️ 可能包含的内容比用户期望的多")

def main():
    """
    主测试函数
    """
    print("🔧 用户反馈问题专项测试")
    print("="*60)
    
    # 测试用户反馈的具体问题
    test_user_reported_issue()
    
    # 提供优化建议
    suggest_content_optimization()
    
    print(f"\n📋 总结:")
    print(f"1. 边界检测是准确的，不会包含其他标题的内容")
    print(f"2. 如果用户觉得内容过多，可能是期望更精简的提取")
    print(f"3. 可以考虑提供不同的提取模式（精简/完整）")

if __name__ == "__main__":
    main()
