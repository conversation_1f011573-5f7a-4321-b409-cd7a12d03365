<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档内容提取工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .heading-item {
            padding: 8px 12px;
            margin: 4px 0;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .heading-item:hover {
            background-color: #f8f9fa;
            border-color: #0d6efd;
        }
        .heading-item.selected {
            background-color: #0d6efd;
            color: white;
            border-color: #0d6efd;
        }
        .heading-item input[type="checkbox"] {
            margin-right: 8px;
        }
        .extraction-progress {
            display: none;
        }
        .progress-item {
            padding: 8px;
            margin: 4px 0;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .progress-item.success {
            background-color: #d1edff;
            border-left: 4px solid #0d6efd;
        }
        .progress-item.error {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .level-1 { margin-left: 0; font-weight: bold; }
        .level-2 { margin-left: 20px; }
        .level-3 { margin-left: 40px; }
        .level-4 { margin-left: 60px; }
        .loading {
            display: none;
        }
        .result-section {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-file-word text-primary"></i>
                    Word文档内容提取工具
                </h1>

                <div class="text-center mb-4">
                    <a href="template.html" class="btn btn-outline-primary">
                        <i class="fas fa-magic"></i> 模板替换工具
                    </a>
                </div>
                
                <!-- 文件上传区域 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-upload"></i> 上传Word文档</h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <p class="mb-2">点击选择文件或拖拽文件到此处</p>
                            <p class="text-muted small">支持 .docx 和 .doc 格式</p>
                            <input type="file" id="fileInput" accept=".docx,.doc" style="display: none;">
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-primary" id="analyzeBtn" disabled>
                                <i class="fas fa-search"></i> 分析文档
                            </button>
                            <div class="loading d-inline-block ms-2">
                                <div class="spinner-border spinner-border-sm" role="status">
                                    <span class="visually-hidden">分析中...</span>
                                </div>
                                <span class="ms-2">分析中...</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分析结果 -->
                <div class="result-section">
                    <div class="row">
                        <!-- 文档信息 -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h6><i class="fas fa-info-circle"></i> 文档信息</h6>
                                </div>
                                <div class="card-body">
                                    <div id="documentInfo">
                                        <!-- 文档信息将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 目录结构 -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h6><i class="fas fa-list"></i> 目录结构</h6>
                                    <div>
                                        <button class="btn btn-sm btn-primary" id="selectAllBtn">
                                            <i class="fas fa-check-double"></i> 全选
                                        </button>
                                        <button class="btn btn-sm btn-secondary" id="clearSelectionBtn">
                                            <i class="fas fa-times"></i> 清空
                                        </button>
                                        <button class="btn btn-sm btn-success" id="extractBtn" disabled>
                                            <i class="fas fa-download"></i> 提取选中内容
                                        </button>
                                        <button class="btn btn-sm btn-info" id="batchExtractBtn" disabled>
                                            <i class="fas fa-download"></i> 批量提取
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body" style="max-height: 500px; overflow-y: auto;">
                                    <div class="mb-3">
                                        <input type="text" class="form-control" id="searchInput" placeholder="搜索标题...">
                                    </div>
                                    <div id="tocList">
                                        <!-- 目录列表将在这里显示 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 提取进度 -->
                <div class="card mt-4 extraction-progress" id="extractionProgress">
                    <div class="card-header">
                        <h6><i class="fas fa-spinner fa-spin"></i> 提取进度</h6>
                    </div>
                    <div class="card-body">
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 0%" id="progressBar"></div>
                        </div>
                        <div id="progressList">
                            <!-- 进度列表将在这里显示 -->
                        </div>
                    </div>
                </div>

                <!-- 提取结果 -->
                <div class="card mt-4" id="extractResult" style="display: none;">
                    <div class="card-header">
                        <h6><i class="fas fa-check-circle text-success"></i> 提取完成</h6>
                    </div>
                    <div class="card-body">
                        <div id="extractInfo">
                            <!-- 提取结果信息将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
