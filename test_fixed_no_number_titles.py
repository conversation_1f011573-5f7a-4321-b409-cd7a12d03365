#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的无序号标题提取功能
"""

import requests
import os
from docx import Document

def test_fixed_no_number_titles():
    """
    测试修复后的无序号标题提取
    """
    print("🔧 测试修复后的无序号标题提取功能")
    print("="*60)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return
    
    # 测试清理后的标题（去掉TOC格式和页码）
    test_cases = [
        {
            "original": "TOC \"1-3\" 网络安全等级测评基本信息表 I",
            "cleaned": "网络安全等级测评基本信息表",
            "description": "TOC格式标题"
        },
        {
            "original": "声明 II", 
            "cleaned": "声明",
            "description": "带页码的标题"
        },
        {
            "original": "等级测评结论 III",
            "cleaned": "等级测评结论", 
            "description": "带页码的标题"
        },
        {
            "original": "重大风险隐患及整改建议 IV",
            "cleaned": "重大风险隐患及整改建议",
            "description": "带页码的长标题"
        },
        {
            "original": "等级测评结论扩展表（云计算安全） V",
            "cleaned": "等级测评结论扩展表（云计算安全）",
            "description": "带括号和页码的标题"
        }
    ]
    
    base_url = "http://localhost:5000/api/wordextraction"
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['description']}")
        print(f"   原始标题: {test_case['original']}")
        print(f"   清理后标题: {test_case['cleaned']}")
        print("-" * 50)
        
        try:
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': test_case['cleaned'],  # 使用清理后的标题
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存文件
                safe_title = test_case['cleaned'].replace('/', '_').replace('\\', '_').replace(':', '_').replace('.', '_').replace(' ', '_')
                output_file = f"修复测试_{safe_title}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                # 分析文件
                doc = Document(output_file)
                file_size = os.path.getsize(output_file)
                
                print(f"   ✅ 提取成功: {output_file}")
                print(f"   📄 段落数: {len(doc.paragraphs)}")
                print(f"   📊 表格数: {len(doc.tables)}")
                print(f"   📁 文件大小: {file_size:,} 字节")
                
                # 检查内容质量
                all_text = " ".join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
                print(f"   📝 内容长度: {len(all_text)} 字符")
                
                if len(all_text) > 100:
                    print(f"   📖 内容预览: {all_text[:150]}...")
                    success_count += 1
                    print(f"   ✅ 内容质量: 良好")
                elif len(all_text) > 20:
                    print(f"   📖 内容预览: {all_text}")
                    print(f"   ⚠️ 内容质量: 一般（内容较少）")
                else:
                    print(f"   📖 内容预览: {all_text}")
                    print(f"   ❌ 内容质量: 差（内容太少）")
                
            else:
                print(f"   ❌ 提取失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 提取出错: {str(e)}")
    
    print(f"\n{'='*60}")
    print(f"📊 修复后测试结果: {success_count}/{len(test_cases)} 完全成功")
    
    if success_count == len(test_cases):
        print("🎉 所有无序号标题提取功能已完全修复！")
    elif success_count >= len(test_cases) * 0.8:
        print("✅ 大部分无序号标题提取功能已修复，少量需要进一步优化。")
    else:
        print("⚠️ 无序号标题提取功能仍需要进一步改进。")
    
    return success_count

def test_api_with_cleaned_titles():
    """
    测试API是否能正确处理清理后的标题
    """
    print(f"\n🌐 测试API标题清理功能")
    print("="*50)
    
    source_file = "测评报告.docx"
    base_url = "http://localhost:5000/api/wordextraction"
    
    # 测试原始格式的标题（应该被自动清理）
    original_titles = [
        "声明 II",
        "等级测评结论 III", 
        "重大风险隐患及整改建议 IV"
    ]
    
    for title in original_titles:
        print(f"\n   测试原始标题: {title}")
        
        try:
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': title,  # 使用原始标题，看API是否能自动清理
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                print(f"     ✅ API自动清理成功")
            else:
                print(f"     ❌ API处理失败: {response.status_code}")
                
        except Exception as e:
            print(f"     ❌ 测试出错: {str(e)}")

def main():
    """
    主测试函数
    """
    print("🚀 无序号标题提取功能修复验证")
    print("="*60)
    
    # 测试修复后的功能
    success_count = test_fixed_no_number_titles()
    
    # 测试API自动清理功能
    test_api_with_cleaned_titles()
    
    print(f"\n💡 总结:")
    print(f"1. 标题清理功能: 移除TOC格式和页码")
    print(f"2. 文件名清理功能: 处理特殊字符")
    print(f"3. 内容边界优化: 根据标题类型调整提取范围")
    
    if success_count >= 4:
        print(f"🎊 无序号标题提取功能基本修复完成！")
    else:
        print(f"🔧 仍需要进一步优化内容边界检测。")

if __name__ == "__main__":
    main()
