{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["vBDhTPsFYhCgAYsBda32ozfvIfXhovQe4XDYxpUX9Tc=", "I9iSuAWlZ04WkLuYyE4Yl6tGvAgmvP0u0iP9Kolnhk0="], "CachedAssets": {"I9iSuAWlZ04WkLuYyE4Yl6tGvAgmvP0u0iP9Kolnhk0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-h77qf1ra26.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint=h77qf1ra26}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tpbcgmyjx9", "Integrity": "rzGPGzISRhXBSCog8sB0nI7ONV44ZB0eH1VCXxV9n5k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "FileLength": 1913, "LastWriteTime": "2025-06-09T09:06:45.9512562+00:00"}, "vBDhTPsFYhCgAYsBda32ozfvIfXhovQe4XDYxpUX9Tc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint=v20ta0p5ky}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z259h50fpz", "Integrity": "+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "FileLength": 4971, "LastWriteTime": "2025-06-09T09:06:45.9622875+00:00"}}, "CachedCopyCandidates": {}}