{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["HPHmi8SdCjctWQlSftbyXcRVbn70jhAMew4hPymEeZQ=", "gka9qa+/dv7hLER9OoGMPdkCwytRY2W/ET8wlMbJ8zI="], "CachedAssets": {"HPHmi8SdCjctWQlSftbyXcRVbn70jhAMew4hPymEeZQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-k6oxa3pozb.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint=k6oxa3pozb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hdm46t5nwi", "Integrity": "RbSM22zIc81lwTzmZ2yOZHXRyayNh0PGIoxH0sFeFWk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "FileLength": 3253, "LastWriteTime": "2025-06-09T08:36:25.8982422+00:00"}, "gka9qa+/dv7hLER9OoGMPdkCwytRY2W/ET8wlMbJ8zI=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-gyy8itz0sx.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint=gyy8itz0sx}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "at5m72mjla", "Integrity": "7pX/I+NEQPlp3DEbP3Yhg9bxiDMpIP5kcHD/BJtZ08A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "FileLength": 1566, "LastWriteTime": "2025-06-09T08:36:25.8988274+00:00"}}, "CachedCopyCandidates": {}}