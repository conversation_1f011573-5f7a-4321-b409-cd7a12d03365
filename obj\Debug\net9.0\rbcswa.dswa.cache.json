{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["c1Bo+hi6sg7MeiM//Rmznyr4gLGMDC0b2UvNkvOBUq8=", "vBDhTPsFYhCgAYsBda32ozfvIfXhovQe4XDYxpUX9Tc=", "vG91eEwAPjJkAbLM9NSXbstOMiJqvcUlKssZftE8Sc0=", "pVdvyRV+KIxDeV6dERIoSASLnktNJ+ml8ptlPNOJj/4="], "CachedAssets": {"c1Bo+hi6sg7MeiM//Rmznyr4gLGMDC0b2UvNkvOBUq8=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-f31xul4g0z.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint=f31xul4g0z}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d3xy7z5skb", "Integrity": "Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "FileLength": 1978, "LastWriteTime": "2025-06-09T10:35:11.827103+00:00"}, "vBDhTPsFYhCgAYsBda32ozfvIfXhovQe4XDYxpUX9Tc=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint=v20ta0p5ky}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z259h50fpz", "Integrity": "+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "FileLength": 4971, "LastWriteTime": "2025-06-09T09:06:45.9622875+00:00"}, "vG91eEwAPjJkAbLM9NSXbstOMiJqvcUlKssZftE8Sc0=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\tbtfvwlp9r-7q71xfk5fx.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "template#[.{fingerprint=7q71xfk5fx}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bz8lz53z9k", "Integrity": "SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.html", "FileLength": 2135, "LastWriteTime": "2025-06-09T10:35:11.8279284+00:00"}, "pVdvyRV+KIxDeV6dERIoSASLnktNJ+ml8ptlPNOJj/4=": {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\hyx4dqjg3v-g0zvhtklle.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "template#[.{fingerprint=g0zvhtklle}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u93bzp51n4", "Integrity": "UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.js", "FileLength": 2479, "LastWriteTime": "2025-06-09T10:35:11.831501+00:00"}}, "CachedCopyCandidates": {}}