#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的模板替换功能
验证替换后的内容不包含标题本身
"""

import requests
import os
from docx import Document

def test_template_replacement_fix():
    """
    测试修复后的模板替换功能
    """
    print("🔧 测试修复后的模板替换功能")
    print("="*60)
    
    # 检查文件是否存在
    template_file = "简单模板.docx"
    reference_file = "测评报告.docx"
    
    if not os.path.exists(template_file):
        print(f"❌ 模板文件不存在: {template_file}")
        return
    
    if not os.path.exists(reference_file):
        print(f"❌ 参考文件不存在: {reference_file}")
        return
    
    print(f"📋 使用文件:")
    print(f"   模板文件: {template_file}")
    print(f"   参考文件: {reference_file}")
    
    # 首先查看模板内容
    print(f"\n📄 模板文档内容:")
    try:
        template_doc = Document(template_file)
        for i, para in enumerate(template_doc.paragraphs):
            text = para.text.strip()
            if text:
                print(f"   段落 {i+1}: {text}")
    except Exception as e:
        print(f"   ❌ 读取模板失败: {str(e)}")
    
    # 执行模板替换
    print(f"\n🔄 执行模板替换...")
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        with open(template_file, 'rb') as tf, open(reference_file, 'rb') as rf:
            files = {
                'templateFile': tf,
                'referenceFile': rf
            }
            response = requests.post(f"{base_url}/replace-template", files=files)
        
        if response.status_code == 200:
            # 保存结果文件
            output_file = "替换结果_测试.docx"
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 模板替换成功: {output_file}")
            
            # 分析替换结果
            analyze_replacement_result(output_file)
            
        else:
            print(f"❌ 模板替换失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 模板替换出错: {str(e)}")

def analyze_replacement_result(output_file):
    """
    分析替换结果，检查是否正确去除了标题
    """
    print(f"\n📊 分析替换结果:")
    print("-" * 40)
    
    try:
        doc = Document(output_file)
        file_size = os.path.getsize(output_file)
        
        print(f"📁 文件大小: {file_size:,} 字节")
        print(f"📄 段落数: {len(doc.paragraphs)}")
        print(f"📊 表格数: {len(doc.tables)}")
        
        print(f"\n📄 替换后的文档内容:")
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                # 检查是否包含标题
                is_title = any(keyword in text for keyword in [
                    "测评目的", "测评依据", "声明", "1.1", "1.2"
                ])
                
                marker = " ⚠️ 可能是标题" if is_title else ""
                print(f"   段落 {i+1}: {text[:100]}{'...' if len(text) > 100 else ''}{marker}")
        
        # 检查表格内容
        if doc.tables:
            print(f"\n📊 表格内容:")
            for i, table in enumerate(doc.tables):
                print(f"   表格 {i+1}: {len(table.rows)} 行 x {len(table.columns)} 列")
                
                # 显示表格前几行
                for row_idx, row in enumerate(table.rows[:2]):
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text[:20] + ('...' if len(cell_text) > 20 else ''))
                    if row_text:
                        print(f"     行 {row_idx+1}: {' | '.join(row_text)}")
        
        # 验证修复效果
        verify_fix_effectiveness(doc)
        
    except Exception as e:
        print(f"❌ 分析结果时出错: {str(e)}")

def verify_fix_effectiveness(doc):
    """
    验证修复效果是否符合预期
    """
    print(f"\n✅ 修复效果验证:")
    print("-" * 30)
    
    all_text = " ".join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
    
    # 检查是否还包含标题文本
    title_indicators = [
        "1.1 测评目的",
        "1.2 测评依据", 
        "测评目的：",
        "测评依据：",
        "声明："
    ]
    
    found_titles = []
    for indicator in title_indicators:
        if indicator in all_text:
            found_titles.append(indicator)
    
    if found_titles:
        print(f"⚠️ 仍包含标题文本: {found_titles}")
        print(f"💡 可能需要进一步优化标题识别逻辑")
    else:
        print(f"✅ 成功去除标题文本，只保留内容")
    
    # 检查内容是否合理
    if len(all_text) > 100:
        print(f"✅ 内容长度合理: {len(all_text)} 字符")
        print(f"📖 内容预览: {all_text[:200]}...")
    elif len(all_text) > 20:
        print(f"⚠️ 内容较少: {len(all_text)} 字符")
        print(f"📖 完整内容: {all_text}")
    else:
        print(f"❌ 内容过少: {len(all_text)} 字符")
        print(f"📖 完整内容: {all_text}")

def create_better_test_template():
    """
    创建更好的测试模板，明确显示期望的替换效果
    """
    print(f"\n📝 创建更好的测试模板")
    print("-" * 40)
    
    doc = Document()
    
    # 添加标题
    doc.add_heading('模板替换测试文档', 0)
    
    # 添加说明
    doc.add_paragraph('本文档用于测试模板替换功能，验证标签是否被正确替换为内容（不包含标题）。')
    
    # 测试用例1：段落中的标签
    doc.add_heading('测试用例1：段落中的标签', level=1)
    doc.add_paragraph('本节测评目的如下：{测评项目概述-测评目的}')
    doc.add_paragraph('期望结果：标签被替换为测评目的的内容，但不包含"1.1 测评目的"这个标题。')
    
    # 测试用例2：独立段落的标签
    doc.add_heading('测试用例2：独立段落的标签', level=1)
    doc.add_paragraph('测评依据：')
    doc.add_paragraph('{测评项目概述-测评依据}')
    doc.add_paragraph('期望结果：标签被替换为测评依据的内容，但不包含"1.2 测评依据"这个标题。')
    
    # 测试用例3：声明内容
    doc.add_heading('测试用例3：声明内容', level=1)
    doc.add_paragraph('机构声明：')
    doc.add_paragraph('{声明}')
    doc.add_paragraph('期望结果：标签被替换为声明的完整内容，但不包含"声明"这个标题。')
    
    # 保存文档
    filename = '测试模板_修复验证.docx'
    doc.save(filename)
    
    print(f"✅ 测试模板已创建: {filename}")
    return filename

def test_with_better_template():
    """
    使用更好的测试模板进行测试
    """
    print(f"\n🧪 使用改进的测试模板进行验证")
    print("="*60)
    
    # 创建更好的测试模板
    template_file = create_better_test_template()
    reference_file = "测评报告.docx"
    
    if not os.path.exists(reference_file):
        print(f"❌ 参考文件不存在: {reference_file}")
        return
    
    # 执行模板替换
    print(f"\n🔄 执行模板替换...")
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        with open(template_file, 'rb') as tf, open(reference_file, 'rb') as rf:
            files = {
                'templateFile': tf,
                'referenceFile': rf
            }
            response = requests.post(f"{base_url}/replace-template", files=files)
        
        if response.status_code == 200:
            # 保存结果文件
            output_file = "替换结果_修复验证.docx"
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 模板替换成功: {output_file}")
            
            # 详细分析结果
            analyze_replacement_result(output_file)
            
        else:
            print(f"❌ 模板替换失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        print(f"❌ 模板替换出错: {str(e)}")

def main():
    """
    主测试函数
    """
    print("🔧 模板替换功能修复验证")
    print("="*60)
    
    # 测试现有的简单模板
    test_template_replacement_fix()
    
    # 使用改进的测试模板进行更详细的验证
    test_with_better_template()
    
    print(f"\n💡 验证要点:")
    print(f"1. 替换后的内容不应包含标题文本（如'1.1 测评目的'）")
    print(f"2. 应该只包含标题下的实际内容")
    print(f"3. 格式应该完整保留（段落、表格、图片等）")
    print(f"4. 标签位置的上下文应该保持连贯")

if __name__ == "__main__":
    main()
