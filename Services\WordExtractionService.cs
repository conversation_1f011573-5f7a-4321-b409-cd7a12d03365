using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordExtractor.Models;
using System.Text.RegularExpressions;

namespace WordExtractor.Services
{
    public class WordExtractionService : IWordExtractionService
    {
        public async Task<DocumentAnalysisResult> AnalyzeDocumentAsync(Stream stream)
        {
            var result = new DocumentAnalysisResult();
            
            try
            {
                using var document = WordprocessingDocument.Open(stream, false);
                var body = document.MainDocumentPart?.Document?.Body;
                
                if (body == null)
                    return result;

                var paragraphs = body.Elements<Paragraph>().ToList();
                result.TotalParagraphs = paragraphs.Count;
                result.TotalTables = body.Elements<Table>().Count();

                // 分析段落和标题
                for (int i = 0; i < paragraphs.Count; i++)
                {
                    var paragraph = paragraphs[i];
                    var text = GetParagraphText(paragraph);
                    
                    if (string.IsNullOrWhiteSpace(text))
                        continue;

                    var styleName = GetParagraphStyleName(paragraph);
                    var isHeading = IsHeadingStyle(styleName);
                    var level = GetHeadingLevel(styleName);

                    var heading = new DocumentHeading
                    {
                        Index = i,
                        Text = text,
                        StyleName = styleName,
                        Level = level,
                        IsHeading = isHeading
                    };

                    result.Headings.Add(heading);

                    // 检查是否为目录项
                    if (IsTocStyle(styleName) || IsTocContent(text))
                    {
                        var tocEntry = ParseTocEntry(text, styleName, i);
                        if (tocEntry != null)
                        {
                            result.TocEntries.Add(tocEntry);
                        }
                    }

                    // 统计标题级别
                    if (isHeading && level > 0)
                    {
                        if (!result.HeadingLevelCounts.ContainsKey(level))
                            result.HeadingLevelCounts[level] = 0;
                        result.HeadingLevelCounts[level]++;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"分析文档时出错: {ex.Message}", ex);
            }
        }

        public async Task<byte[]> ExtractSectionWithFormatAsync(Stream stream, string targetTitle)
        {
            try
            {
                // 创建内存流来处理文档
                using var memoryStream = new MemoryStream();
                await stream.CopyToAsync(memoryStream);
                memoryStream.Position = 0;

                // 打开源文档
                using var sourceDocument = WordprocessingDocument.Open(memoryStream, false);
                var sourceBody = sourceDocument.MainDocumentPart?.Document?.Body;
                
                if (sourceBody == null)
                    throw new InvalidOperationException("无法读取源文档内容");

                // 查找目标标题的位置
                var (startIndex, endIndex, relatedTables) = FindSectionBoundaries(sourceBody, targetTitle);
                
                if (startIndex == -1)
                    throw new InvalidOperationException($"未找到标题: {targetTitle}");

                // 创建新文档
                using var newDocumentStream = new MemoryStream();
                using (var newDocument = WordprocessingDocument.Create(newDocumentStream, WordprocessingDocumentType.Document))
                {
                    // 复制文档部分和样式
                    CopyDocumentStructure(sourceDocument, newDocument);
                    
                    var newBody = newDocument.MainDocumentPart!.Document.Body!;
                    
                    // 复制指定范围的内容，保留完整格式
                    CopySectionWithFormat(sourceBody, newBody, startIndex, endIndex, relatedTables);
                    
                    newDocument.Save();
                }

                return newDocumentStream.ToArray();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"提取内容时出错: {ex.Message}", ex);
            }
        }

        public async Task<List<DocumentHeading>> GetDocumentHeadingsAsync(Stream stream)
        {
            var analysis = await AnalyzeDocumentAsync(stream);
            return analysis.Headings.Where(h => h.IsHeading).ToList();
        }

        private string GetParagraphText(Paragraph paragraph)
        {
            return paragraph.InnerText?.Trim() ?? string.Empty;
        }

        private string GetParagraphStyleName(Paragraph paragraph)
        {
            var styleId = paragraph.ParagraphProperties?.ParagraphStyleId?.Val?.Value;
            if (string.IsNullOrEmpty(styleId))
                return "Normal";

            // 尝试从文档中获取样式的实际名称
            var document = paragraph.Ancestors<Document>().FirstOrDefault();
            if (document != null)
            {
                var styles = document.MainDocumentPart?.StyleDefinitionsPart?.Styles;
                if (styles != null)
                {
                    var style = styles.Elements<Style>().FirstOrDefault(s => s.StyleId == styleId);
                    if (style?.StyleName?.Val?.Value != null)
                    {
                        return style.StyleName.Val.Value;
                    }
                }
            }

            // 如果找不到样式名称，返回样式ID
            return styleId;
        }

        private bool IsHeadingStyle(string styleName)
        {
            return styleName.StartsWith("Heading", StringComparison.OrdinalIgnoreCase) ||
                   styleName.Contains("标题", StringComparison.OrdinalIgnoreCase) ||
                   styleName.Equals("Title", StringComparison.OrdinalIgnoreCase);
        }

        private int GetHeadingLevel(string styleName)
        {
            if (styleName.Equals("Title", StringComparison.OrdinalIgnoreCase))
                return 0;

            var match = Regex.Match(styleName, @"(\d+)");
            if (match.Success && int.TryParse(match.Value, out int level))
                return level;

            return 1;
        }

        private bool IsTocStyle(string styleName)
        {
            return styleName.StartsWith("toc", StringComparison.OrdinalIgnoreCase) ||
                   styleName.Contains("目录", StringComparison.OrdinalIgnoreCase) ||
                   styleName.Contains("contents", StringComparison.OrdinalIgnoreCase);
        }

        private bool IsTocContent(string text)
        {
            var tocKeywords = new[] { "目录", "contents", "table of contents", "目　录" };
            return tocKeywords.Any(keyword => 
                text.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private TocEntry? ParseTocEntry(string text, string styleName, int index)
        {
            // 解析目录项的正则表达式模式
            var patterns = new[]
            {
                @"^(.+?)\s*\.{2,}\s*(\d+)$",  // 标题....页码
                @"^(.+?)\s+(\d+)$",           // 标题 页码
                @"^(.+?)\t+(\d+)$",           // 标题\t页码
                @"^(.+?)\s*…+\s*(\d+)$",      // 标题…页码
                @"^(.+?)\s*\d+\s*$"           // 标题 数字（简化版）
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(text.Trim(), pattern);
                if (match.Success)
                {
                    var title = match.Groups[1].Value.Trim();
                    var pageNumber = match.Groups.Count > 2 ? match.Groups[2].Value : "";
                    var level = GetTocLevel(styleName);

                    // 清理标题，移除Word内部引用
                    title = CleanTitle(title);

                    // 验证标题的有效性
                    if (IsValidTocTitle(title))
                    {
                        return new TocEntry
                        {
                            Index = index,
                            Text = text,
                            StyleName = styleName,
                            Title = title,
                            PageNumber = pageNumber,
                            Level = level
                        };
                    }
                }
            }

            // 如果是目录样式但没有匹配到标准格式，尝试简单解析
            if (IsTocStyle(styleName))
            {
                var cleanText = CleanTitle(text);
                if (IsValidTocTitle(cleanText))
                {
                    var level = GetTocLevel(styleName);
                    return new TocEntry
                    {
                        Index = index,
                        Text = text,
                        StyleName = styleName,
                        Title = cleanText,
                        PageNumber = "",
                        Level = level
                    };
                }
            }

            return null;
        }

        private bool IsValidTocTitle(string title)
        {
            if (string.IsNullOrWhiteSpace(title) || title.Length < 2)
                return false;

            // 排除明显不是标题的内容
            var invalidPatterns = new[]
            {
                @"^PAGEREF",
                @"^\\",
                @"^\s*\d+\s*$",  // 只有数字
                @"^[\.…\s]+$"    // 只有点号或省略号
            };

            return !invalidPatterns.Any(pattern => Regex.IsMatch(title, pattern, RegexOptions.IgnoreCase));
        }

        private bool IsLikelyContentHeading(string text, string styleName)
        {
            // 判断是否可能是正文中的标题
            if (string.IsNullOrWhiteSpace(text))
                return false;

            // 如果已经是标题样式，直接返回true
            if (IsHeadingStyle(styleName))
                return true;

            // 检查文本模式，判断是否像标题
            var headingPatterns = new[]
            {
                @"^\d+\s+\w+",           // 数字开头的标题 (如 "1 测评项目概述")
                @"^\d+\.\d+\s+\w+",      // 数字.数字开头 (如 "1.1 测评目的")
                @"^\d+\.\d+\.\d+\s+\w+", // 三级编号 (如 "1.1.1 具体内容")
                @"^第\w+章",             // 第X章
                @"^第\w+节",             // 第X节
                @"^\w+概述$",            // XX概述
                @"^\w+说明$",            // XX说明
                @"^\w+分析$"             // XX分析
            };

            return headingPatterns.Any(pattern => Regex.IsMatch(text.Trim(), pattern));
        }

        private string ExtractCoreTitle(string title)
        {
            // 提取标题的核心部分，去掉编号前缀
            if (string.IsNullOrWhiteSpace(title))
                return title;

            // 去掉常见的编号前缀模式
            var patterns = new[]
            {
                @"^\d+\s+",              // "1 " -> ""
                @"^\d+\.\d+\s+",         // "1.1 " -> ""
                @"^\d+\.\d+\.\d+\s+",    // "1.1.1 " -> ""
                @"^\d+\.\d+\.\d+\.\d+\s+", // "1.1.1.1 " -> ""
                @"^第\d+章\s*",          // "第1章 " -> ""
                @"^第\d+节\s*",          // "第1节 " -> ""
                @"^\(\d+\)\s*",          // "(1) " -> ""
                @"^[一二三四五六七八九十]+\s*[、.]\s*", // "一、" -> ""
            };

            var result = title;
            foreach (var pattern in patterns)
            {
                result = Regex.Replace(result, pattern, "", RegexOptions.IgnoreCase);
            }

            return result.Trim();
        }

        private bool IsObviousOtherTitle(string text, string startText)
        {
            // 检查是否是明显的其他标题
            if (string.IsNullOrWhiteSpace(text) || text.Length > 100)
                return false;

            var cleanText = CleanTitle(text).ToLower();
            var cleanStartText = CleanTitle(startText).ToLower();

            // 如果是相同的标题，不算其他标题
            if (cleanText.Equals(cleanStartText, StringComparison.OrdinalIgnoreCase))
                return false;

            // 检查常见的标题模式
            var titlePatterns = new[]
            {
                @"^\d+\.\d+\s+\w+",      // "1.1 测评目的"
                @"^\d+\s+\w+",           // "1 测评项目概述"
                @"^测评\w+$",            // "测评目的", "测评依据", "测评过程"
                @"^被测对象\w+$",        // "被测对象概述", "被测对象描述"
                @"^\w+概述$",            // "项目概述", "系统概述"
                @"^\w+描述$",            // "对象描述", "系统描述"
                @"^\w+结论$",            // "测评结论", "评估结论"
                @"^\w+建议$",            // "整改建议", "优化建议"
                @"^\w+过程$",            // "测评过程", "实施过程"
                @"^\w+依据$",            // "测评依据", "评估依据"
            };

            foreach (var pattern in titlePatterns)
            {
                if (Regex.IsMatch(cleanText, pattern))
                {
                    return true;
                }
            }

            return false;
        }

        private int GetTocLevel(string styleName)
        {
            var match = Regex.Match(styleName, @"(\d+)");
            if (match.Success && int.TryParse(match.Value, out int level))
                return level;
            return 1;
        }

        private (int startIndex, int endIndex, List<int> relatedTables) FindSectionBoundaries(Body body, string targetTitle)
        {
            var paragraphs = body.Elements<Paragraph>().ToList();
            var tables = body.Elements<Table>().ToList();

            int startIndex = -1;
            int endIndex = -1;
            var relatedTables = new List<int>();

            // 清理目标标题，移除可能的Word内部引用
            var cleanTargetTitle = CleanTitle(targetTitle);

            // 查找目标标题 - 优先匹配正文标题，避免匹配目录
            // 提取标题的核心部分（去掉编号前缀）
            var coreTitle = ExtractCoreTitle(cleanTargetTitle);

            for (int i = 0; i < paragraphs.Count; i++)
            {
                var text = GetParagraphText(paragraphs[i]);
                var cleanText = CleanTitle(text);
                var styleName = GetParagraphStyleName(paragraphs[i]);

                // 跳过目录样式的段落
                if (IsTocStyle(styleName))
                    continue;

                // 优先匹配正文标题样式
                if (IsHeadingStyle(styleName))
                {
                    // 精确匹配或核心标题匹配
                    if (cleanText.Equals(cleanTargetTitle, StringComparison.OrdinalIgnoreCase) ||
                        cleanText.Equals(coreTitle, StringComparison.OrdinalIgnoreCase) ||
                        (cleanText.Contains(coreTitle, StringComparison.OrdinalIgnoreCase) && coreTitle.Length > 3))
                    {
                        startIndex = i;
                        break;
                    }
                }

                // 如果没有找到标题样式，尝试匹配可能的内容标题
                if (IsLikelyContentHeading(text, styleName))
                {
                    if (cleanText.Contains(cleanTargetTitle, StringComparison.OrdinalIgnoreCase) &&
                        cleanTargetTitle.Length > 5)
                    {
                        startIndex = i;
                        break;
                    }
                }
            }

            if (startIndex == -1)
                return (-1, -1, relatedTables);

            // 查找结束位置 - 更精确的边界检测
            var startLevel = GetParagraphHeadingLevel(paragraphs[startIndex]);
            var startText = GetParagraphText(paragraphs[startIndex]).ToLower();
            var startStyleName = GetParagraphStyleName(paragraphs[startIndex]);

            // 特殊处理：对于"基本信息表"，提取标题和紧邻的表格
            if (startText.Contains("基本信息") && startText.Contains("表"))
            {
                endIndex = startIndex + 1;
                // 但仍然需要查找相关表格
            }
            else
            {
                // 对于正文标题，查找实际的章节内容
                for (int i = startIndex + 1; i < paragraphs.Count; i++)
                {
                    var text = GetParagraphText(paragraphs[i]);
                    var styleName = GetParagraphStyleName(paragraphs[i]);
                    var currentLevel = GetParagraphHeadingLevel(paragraphs[i]);

                    // 跳过目录样式的段落
                    if (IsTocStyle(styleName))
                        continue;

                    // 如果遇到同级或更高级的正文标题，则结束
                    if ((IsHeadingStyle(styleName) || IsLikelyContentHeading(text, styleName)) &&
                        currentLevel > 0 && currentLevel <= startLevel)
                    {
                        endIndex = i;
                        break;
                    }

                    // 额外检查：如果遇到明显的其他标题，也要结束
                    if (!string.IsNullOrWhiteSpace(text) && IsObviousOtherTitle(text, startText))
                    {
                        endIndex = i;
                        break;
                    }

                    // 如果遇到明确的章节分界，则结束
                    if (!string.IsNullOrWhiteSpace(text))
                    {
                        var cleanText = CleanTitle(text);

                        // 检查是否为明确的新章节
                        if (cleanText.Equals("目录", StringComparison.OrdinalIgnoreCase) ||
                            cleanText.Equals("声明", StringComparison.OrdinalIgnoreCase) ||
                            cleanText.StartsWith("附录", StringComparison.OrdinalIgnoreCase) ||
                            (IsLikelyContentHeading(cleanText, styleName) && cleanText.Length > 5))
                        {
                            endIndex = i;
                            break;
                        }
                    }
                }

                // 如果没有找到结束位置，使用保守的范围避免包含过多内容
                if (endIndex == -1)
                {
                    // 根据标题级别和标题类型决定范围，使用更保守的策略
                    var titleText = GetParagraphText(paragraphs[startIndex]).ToLower();
                    int range;

                    // 对于特殊标题，使用适中的范围
                    if (titleText.Contains("结论") || titleText.Contains("建议") ||
                        titleText.Contains("声明") || titleText.Contains("扩展表"))
                    {
                        range = 50; // 这些通常内容较多，但要避免包含其他标题
                    }
                    else if (titleText.Contains("概述") || titleText.Contains("描述"))
                    {
                        range = 30; // 概述和描述通常较短
                    }
                    else if (startLevel <= 1)
                    {
                        range = 40; // 一级标题，保守范围
                    }
                    else if (startLevel == 2)
                    {
                        range = 25; // 二级标题，更保守
                    }
                    else
                    {
                        range = 15; // 三级及以下标题，最保守
                    }

                    endIndex = Math.Min(startIndex + range, paragraphs.Count);
                }
            }

            // 查找相关表格 - 更精确的表格关联
            relatedTables = FindRelatedTables(body, startIndex, endIndex);

            return (startIndex, endIndex, relatedTables);
        }

        private string CleanTitle(string title)
        {
            if (string.IsNullOrWhiteSpace(title))
                return string.Empty;

            // 移除Word内部引用和格式标记
            var cleaned = title.Trim();

            // 移除TOC格式代码 (如 TOC "1-3")
            cleaned = Regex.Replace(cleaned, @"TOC\s+""[^""]*""\s*", "", RegexOptions.IgnoreCase);

            // 移除PAGEREF引用
            cleaned = Regex.Replace(cleaned, @"PAGEREF\s+\w+\s+\\h", "", RegexOptions.IgnoreCase);

            // 移除其他Word字段代码
            cleaned = Regex.Replace(cleaned, @"\\[a-zA-Z]+\s*", "");

            // 移除页码信息 (如结尾的罗马数字 I, II, III, IV, V, VI, VII, VIII, IX, X 等)
            cleaned = Regex.Replace(cleaned, @"\s+[IVX]+\s*$", "", RegexOptions.IgnoreCase);

            // 移除阿拉伯数字页码 (如结尾的 1, 2, 3 等)
            cleaned = Regex.Replace(cleaned, @"\s+\d+\s*$", "");

            // 移除多余的空格和特殊字符
            cleaned = Regex.Replace(cleaned, @"\s+", " ");
            cleaned = cleaned.Trim();

            return cleaned;
        }

        private int GetParagraphHeadingLevel(Paragraph paragraph)
        {
            var styleName = GetParagraphStyleName(paragraph);
            return GetHeadingLevel(styleName);
        }

        private bool IsLikelyNewSection(string text)
        {
            // 检查是否像是新章节的开始
            var patterns = new[]
            {
                @"^\d+\s+\w+",           // 数字开头的标题
                @"^第\w+章",             // 第X章
                @"^第\w+节",             // 第X节
                @"^\w+概述$",            // XX概述
                @"^\w+说明$",            // XX说明
                @"^\w+分析$"             // XX分析
            };

            return patterns.Any(pattern => Regex.IsMatch(text, pattern));
        }

        private List<int> FindRelatedTables(Body body, int startIndex, int endIndex)
        {
            var relatedTables = new List<int>();
            var allElements = body.Elements().ToList();
            var paragraphs = body.Elements<Paragraph>().ToList();
            var tables = body.Elements<Table>().ToList();

            // 特殊处理：对于"基本信息表"这类标题，包含紧邻的表格
            if (startIndex >= 0 && startIndex < paragraphs.Count)
            {
                var titleText = GetParagraphText(paragraphs[startIndex]).ToLower();
                if (titleText.Contains("基本信息") && titleText.Contains("表"))
                {
                    // 查找标题后最近的表格
                    var titleElementIndex = -1;
                    for (int i = 0; i < allElements.Count; i++)
                    {
                        if (allElements[i] is Paragraph para && paragraphs.IndexOf(para) == startIndex)
                        {
                            titleElementIndex = i;
                            break;
                        }
                    }

                    if (titleElementIndex != -1)
                    {
                        // 查找标题后的表格（扩大搜索范围）
                        for (int i = titleElementIndex + 1; i < Math.Min(titleElementIndex + 20, allElements.Count); i++)
                        {
                            if (allElements[i] is Table table)
                            {
                                var tableIndex = tables.IndexOf(table);
                                if (tableIndex >= 0)
                                    relatedTables.Add(tableIndex);
                            }
                        }
                    }
                    return relatedTables;
                }
            }

            // 找到开始和结束段落在所有元素中的位置
            int startElementIndex = -1;
            int endElementIndex = -1;

            for (int i = 0; i < allElements.Count; i++)
            {
                if (allElements[i] is Paragraph para)
                {
                    var paraIndex = paragraphs.IndexOf(para);
                    if (paraIndex == startIndex && startElementIndex == -1)
                        startElementIndex = i;
                    if (paraIndex == endIndex - 1)
                        endElementIndex = i;
                }
            }

            // 扩大搜索范围，确保包含相关表格
            if (endElementIndex != -1)
            {
                endElementIndex = Math.Min(endElementIndex + 30, allElements.Count - 1);
            }

            // 查找在扩展范围内的表格
            for (int i = 0; i < allElements.Count; i++)
            {
                if (allElements[i] is Table table)
                {
                    var tableIndex = tables.IndexOf(table);

                    // 如果表格在我们的范围内或紧邻范围
                    if (startElementIndex != -1 &&
                        i >= startElementIndex &&
                        (endElementIndex == -1 || i <= endElementIndex))
                    {
                        if (tableIndex >= 0)
                            relatedTables.Add(tableIndex);
                    }
                }
            }

            return relatedTables;
        }

        private void CopyDocumentStructure(WordprocessingDocument source, WordprocessingDocument target)
        {
            // 创建主文档部分
            var mainPart = target.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            // 复制样式
            if (source.MainDocumentPart?.StyleDefinitionsPart != null)
            {
                var stylesPart = mainPart.AddNewPart<StyleDefinitionsPart>();
                stylesPart.Styles = (Styles)source.MainDocumentPart.StyleDefinitionsPart.Styles.CloneNode(true);
            }

            // 复制字体表
            if (source.MainDocumentPart?.FontTablePart != null)
            {
                var fontTablePart = mainPart.AddNewPart<FontTablePart>();
                fontTablePart.Fonts = (Fonts)source.MainDocumentPart.FontTablePart.Fonts.CloneNode(true);
            }

            // 复制主题
            if (source.MainDocumentPart?.ThemePart != null)
            {
                var themePart = mainPart.AddNewPart<ThemePart>();
                using var themeStream = source.MainDocumentPart.ThemePart.GetStream();
                themePart.FeedData(themeStream);
            }

            // 复制编号定义（用于列表）
            if (source.MainDocumentPart?.NumberingDefinitionsPart != null)
            {
                var numberingPart = mainPart.AddNewPart<NumberingDefinitionsPart>();
                numberingPart.Numbering = (Numbering)source.MainDocumentPart.NumberingDefinitionsPart.Numbering.CloneNode(true);
            }

            // 复制图片和媒体资源
            CopyImageParts(source.MainDocumentPart, mainPart);
        }

        private void CopyImageParts(MainDocumentPart? sourcePart, MainDocumentPart targetPart)
        {
            if (sourcePart == null) return;

            // 复制所有图片部分
            foreach (var imagePart in sourcePart.ImageParts)
            {
                var newImagePart = targetPart.AddImagePart(imagePart.ContentType);
                using var imageStream = imagePart.GetStream();
                newImagePart.FeedData(imageStream);
            }

            // 复制其他媒体相关部分（简化处理）
            try
            {
                foreach (var part in sourcePart.Parts)
                {
                    // 跳过已经处理的图片部分和其他已知部分
                    if (part.OpenXmlPart is ImagePart ||
                        part.OpenXmlPart is StyleDefinitionsPart ||
                        part.OpenXmlPart is FontTablePart ||
                        part.OpenXmlPart is ThemePart ||
                        part.OpenXmlPart is NumberingDefinitionsPart)
                        continue;

                    // 这里可以根据需要添加其他媒体类型的处理
                }
            }
            catch
            {
                // 忽略媒体复制错误，确保基本功能正常
            }
        }

        private void CopySectionWithFormat(Body sourceBody, Body targetBody, int startIndex, int endIndex, List<int> tableIndices)
        {
            var sourceParagraphs = sourceBody.Elements<Paragraph>().ToList();
            var sourceTables = sourceBody.Elements<Table>().ToList();
            var allElements = sourceBody.Elements().ToList();

            // 方法1: 复制段落范围内的所有元素（包括图片、表格、其他格式）
            CopyElementsInParagraphRange(allElements, sourceParagraphs, targetBody, startIndex, endIndex);

            // 方法2: 复制额外的相关表格
            CopyAdditionalTables(sourceTables, targetBody, tableIndices, startIndex, endIndex);
        }

        private void CopyElementsInParagraphRange(List<OpenXmlElement> allElements, List<Paragraph> paragraphs,
                                                Body targetBody, int startIndex, int endIndex)
        {
            // 找到开始和结束段落在所有元素中的位置
            int startElementIndex = -1;
            int endElementIndex = -1;

            for (int i = 0; i < allElements.Count; i++)
            {
                if (allElements[i] is Paragraph para)
                {
                    var paraIndex = paragraphs.IndexOf(para);
                    if (paraIndex == startIndex && startElementIndex == -1)
                        startElementIndex = i;
                    if (paraIndex == endIndex - 1)
                        endElementIndex = i;
                }
            }

            // 如果找到了范围，复制范围内的所有元素
            if (startElementIndex != -1)
            {
                if (endElementIndex == -1)
                    endElementIndex = allElements.Count - 1;

                for (int i = startElementIndex; i <= endElementIndex && i < allElements.Count; i++)
                {
                    var element = allElements[i];

                    // 跳过空段落，但保留其他所有元素
                    if (element is Paragraph para)
                    {
                        var text = GetParagraphText(para);
                        if (string.IsNullOrWhiteSpace(text))
                            continue;
                    }

                    // 深度克隆元素以保持所有格式、图片、表格等
                    var clonedElement = element.CloneNode(true);
                    targetBody.AppendChild(clonedElement);
                }
            }
            else
            {
                // 如果没有找到范围，回退到原来的段落复制方法
                for (int i = startIndex; i < endIndex && i < paragraphs.Count; i++)
                {
                    var sourceParagraph = paragraphs[i];
                    var text = GetParagraphText(sourceParagraph);

                    // 跳过空段落
                    if (string.IsNullOrWhiteSpace(text))
                        continue;

                    // 深度克隆段落以保持所有格式
                    var clonedParagraph = (Paragraph)sourceParagraph.CloneNode(true);
                    targetBody.AppendChild(clonedParagraph);
                }
            }
        }

        private void CopyAdditionalTables(List<Table> sourceTables, Body targetBody, List<int> tableIndices,
                                        int startIndex, int endIndex)
        {
            // 复制额外指定的表格（不在段落范围内的）
            foreach (var tableIndex in tableIndices)
            {
                if (tableIndex < sourceTables.Count)
                {
                    var sourceTable = sourceTables[tableIndex];

                    // 检查这个表格是否已经在段落范围内被复制了
                    bool alreadyCopied = false;
                    foreach (var existingElement in targetBody.Elements())
                    {
                        if (existingElement is Table &&
                            existingElement.InnerText == sourceTable.InnerText)
                        {
                            alreadyCopied = true;
                            break;
                        }
                    }

                    if (!alreadyCopied)
                    {
                        // 深度克隆表格以保持所有格式、样式和结构
                        var clonedTable = (Table)sourceTable.CloneNode(true);
                        targetBody.AppendChild(clonedTable);
                    }
                }
            }
        }
    }
}
