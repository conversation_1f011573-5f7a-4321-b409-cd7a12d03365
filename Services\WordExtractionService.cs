using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using WordExtractor.Models;
using System.Text.RegularExpressions;

namespace WordExtractor.Services
{
    public class WordExtractionService : IWordExtractionService
    {
        public async Task<DocumentAnalysisResult> AnalyzeDocumentAsync(Stream stream)
        {
            var result = new DocumentAnalysisResult();
            
            try
            {
                using var document = WordprocessingDocument.Open(stream, false);
                var body = document.MainDocumentPart?.Document?.Body;
                
                if (body == null)
                    return result;

                var paragraphs = body.Elements<Paragraph>().ToList();
                result.TotalParagraphs = paragraphs.Count;
                result.TotalTables = body.Elements<Table>().Count();

                // 分析段落和标题
                for (int i = 0; i < paragraphs.Count; i++)
                {
                    var paragraph = paragraphs[i];
                    var text = GetParagraphText(paragraph);
                    
                    if (string.IsNullOrWhiteSpace(text))
                        continue;

                    var styleName = GetParagraphStyleName(paragraph);
                    var isHeading = IsHeadingStyle(styleName);
                    var level = GetHeadingLevel(styleName);

                    var heading = new DocumentHeading
                    {
                        Index = i,
                        Text = text,
                        StyleName = styleName,
                        Level = level,
                        IsHeading = isHeading
                    };

                    result.Headings.Add(heading);

                    // 检查是否为目录项
                    if (IsTocStyle(styleName) || IsTocContent(text))
                    {
                        var tocEntry = ParseTocEntry(text, styleName, i);
                        if (tocEntry != null)
                        {
                            result.TocEntries.Add(tocEntry);
                        }
                    }

                    // 统计标题级别
                    if (isHeading && level > 0)
                    {
                        if (!result.HeadingLevelCounts.ContainsKey(level))
                            result.HeadingLevelCounts[level] = 0;
                        result.HeadingLevelCounts[level]++;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"分析文档时出错: {ex.Message}", ex);
            }
        }

        public async Task<byte[]> ExtractSectionWithFormatAsync(Stream stream, string targetTitle)
        {
            try
            {
                // 创建内存流来处理文档
                using var memoryStream = new MemoryStream();
                await stream.CopyToAsync(memoryStream);
                memoryStream.Position = 0;

                // 打开源文档
                using var sourceDocument = WordprocessingDocument.Open(memoryStream, false);
                var sourceBody = sourceDocument.MainDocumentPart?.Document?.Body;
                
                if (sourceBody == null)
                    throw new InvalidOperationException("无法读取源文档内容");

                // 查找目标标题的位置
                var (startIndex, endIndex, relatedTables) = FindSectionBoundaries(sourceBody, targetTitle);
                
                if (startIndex == -1)
                    throw new InvalidOperationException($"未找到标题: {targetTitle}");

                // 创建新文档
                using var newDocumentStream = new MemoryStream();
                using (var newDocument = WordprocessingDocument.Create(newDocumentStream, WordprocessingDocumentType.Document))
                {
                    // 复制文档部分和样式
                    CopyDocumentStructure(sourceDocument, newDocument);
                    
                    var newBody = newDocument.MainDocumentPart!.Document.Body!;
                    
                    // 复制指定范围的内容，保留完整格式
                    CopySectionWithFormat(sourceBody, newBody, startIndex, endIndex, relatedTables);
                    
                    newDocument.Save();
                }

                return newDocumentStream.ToArray();
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"提取内容时出错: {ex.Message}", ex);
            }
        }

        public async Task<List<DocumentHeading>> GetDocumentHeadingsAsync(Stream stream)
        {
            var analysis = await AnalyzeDocumentAsync(stream);
            return analysis.Headings.Where(h => h.IsHeading).ToList();
        }

        private string GetParagraphText(Paragraph paragraph)
        {
            return paragraph.InnerText?.Trim() ?? string.Empty;
        }

        private string GetParagraphStyleName(Paragraph paragraph)
        {
            var styleId = paragraph.ParagraphProperties?.ParagraphStyleId?.Val?.Value;
            return styleId ?? "Normal";
        }

        private bool IsHeadingStyle(string styleName)
        {
            return styleName.StartsWith("Heading", StringComparison.OrdinalIgnoreCase) ||
                   styleName.Contains("标题", StringComparison.OrdinalIgnoreCase) ||
                   styleName.Equals("Title", StringComparison.OrdinalIgnoreCase);
        }

        private int GetHeadingLevel(string styleName)
        {
            if (styleName.Equals("Title", StringComparison.OrdinalIgnoreCase))
                return 0;

            var match = Regex.Match(styleName, @"(\d+)");
            if (match.Success && int.TryParse(match.Value, out int level))
                return level;

            return 1;
        }

        private bool IsTocStyle(string styleName)
        {
            return styleName.StartsWith("toc", StringComparison.OrdinalIgnoreCase) ||
                   styleName.Contains("目录", StringComparison.OrdinalIgnoreCase) ||
                   styleName.Contains("contents", StringComparison.OrdinalIgnoreCase);
        }

        private bool IsTocContent(string text)
        {
            var tocKeywords = new[] { "目录", "contents", "table of contents", "目　录" };
            return tocKeywords.Any(keyword => 
                text.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        private TocEntry? ParseTocEntry(string text, string styleName, int index)
        {
            // 解析目录项的正则表达式模式
            var patterns = new[]
            {
                @"^(.+?)\s*\.{2,}\s*(\d+)$",  // 标题....页码
                @"^(.+?)\s+(\d+)$",           // 标题 页码
                @"^(.+?)\t+(\d+)$",           // 标题\t页码
                @"^(.+?)\s*…+\s*(\d+)$",      // 标题…页码
                @"^(.+?)\s*\d+\s*$"           // 标题 数字（简化版）
            };

            foreach (var pattern in patterns)
            {
                var match = Regex.Match(text.Trim(), pattern);
                if (match.Success)
                {
                    var title = match.Groups[1].Value.Trim();
                    var pageNumber = match.Groups.Count > 2 ? match.Groups[2].Value : "";
                    var level = GetTocLevel(styleName);

                    // 清理标题，移除Word内部引用
                    title = CleanTitle(title);

                    // 验证标题的有效性
                    if (IsValidTocTitle(title))
                    {
                        return new TocEntry
                        {
                            Index = index,
                            Text = text,
                            StyleName = styleName,
                            Title = title,
                            PageNumber = pageNumber,
                            Level = level
                        };
                    }
                }
            }

            // 如果是目录样式但没有匹配到标准格式，尝试简单解析
            if (IsTocStyle(styleName))
            {
                var cleanText = CleanTitle(text);
                if (IsValidTocTitle(cleanText))
                {
                    var level = GetTocLevel(styleName);
                    return new TocEntry
                    {
                        Index = index,
                        Text = text,
                        StyleName = styleName,
                        Title = cleanText,
                        PageNumber = "",
                        Level = level
                    };
                }
            }

            return null;
        }

        private bool IsValidTocTitle(string title)
        {
            if (string.IsNullOrWhiteSpace(title) || title.Length < 2)
                return false;

            // 排除明显不是标题的内容
            var invalidPatterns = new[]
            {
                @"^PAGEREF",
                @"^\\",
                @"^\s*\d+\s*$",  // 只有数字
                @"^[\.…\s]+$"    // 只有点号或省略号
            };

            return !invalidPatterns.Any(pattern => Regex.IsMatch(title, pattern, RegexOptions.IgnoreCase));
        }

        private bool IsLikelyContentHeading(string text, string styleName)
        {
            // 判断是否可能是正文中的标题
            if (string.IsNullOrWhiteSpace(text))
                return false;

            // 如果已经是标题样式，直接返回true
            if (IsHeadingStyle(styleName))
                return true;

            // 检查文本模式，判断是否像标题
            var headingPatterns = new[]
            {
                @"^\d+\s+\w+",           // 数字开头的标题 (如 "1 测评项目概述")
                @"^\d+\.\d+\s+\w+",      // 数字.数字开头 (如 "1.1 测评目的")
                @"^\d+\.\d+\.\d+\s+\w+", // 三级编号 (如 "1.1.1 具体内容")
                @"^第\w+章",             // 第X章
                @"^第\w+节",             // 第X节
                @"^\w+概述$",            // XX概述
                @"^\w+说明$",            // XX说明
                @"^\w+分析$"             // XX分析
            };

            return headingPatterns.Any(pattern => Regex.IsMatch(text.Trim(), pattern));
        }

        private string ExtractCoreTitle(string title)
        {
            // 提取标题的核心部分，去掉编号前缀
            if (string.IsNullOrWhiteSpace(title))
                return title;

            // 去掉常见的编号前缀模式
            var patterns = new[]
            {
                @"^\d+\s+",              // "1 " -> ""
                @"^\d+\.\d+\s+",         // "1.1 " -> ""
                @"^\d+\.\d+\.\d+\s+",    // "1.1.1 " -> ""
                @"^\d+\.\d+\.\d+\.\d+\s+", // "1.1.1.1 " -> ""
                @"^第\d+章\s*",          // "第1章 " -> ""
                @"^第\d+节\s*",          // "第1节 " -> ""
                @"^\(\d+\)\s*",          // "(1) " -> ""
                @"^[一二三四五六七八九十]+\s*[、.]\s*", // "一、" -> ""
            };

            var result = title;
            foreach (var pattern in patterns)
            {
                result = Regex.Replace(result, pattern, "", RegexOptions.IgnoreCase);
            }

            return result.Trim();
        }

        private int GetTocLevel(string styleName)
        {
            var match = Regex.Match(styleName, @"(\d+)");
            if (match.Success && int.TryParse(match.Value, out int level))
                return level;
            return 1;
        }

        private (int startIndex, int endIndex, List<int> relatedTables) FindSectionBoundaries(Body body, string targetTitle)
        {
            var paragraphs = body.Elements<Paragraph>().ToList();
            var tables = body.Elements<Table>().ToList();

            int startIndex = -1;
            int endIndex = -1;
            var relatedTables = new List<int>();

            // 清理目标标题，移除可能的Word内部引用
            var cleanTargetTitle = CleanTitle(targetTitle);

            // 查找目标标题 - 优先匹配正文标题，避免匹配目录
            // 提取标题的核心部分（去掉编号前缀）
            var coreTitle = ExtractCoreTitle(cleanTargetTitle);

            for (int i = 0; i < paragraphs.Count; i++)
            {
                var text = GetParagraphText(paragraphs[i]);
                var cleanText = CleanTitle(text);
                var styleName = GetParagraphStyleName(paragraphs[i]);

                // 跳过目录样式的段落
                if (IsTocStyle(styleName))
                    continue;

                // 优先匹配正文标题样式
                if (IsHeadingStyle(styleName))
                {
                    // 精确匹配或核心标题匹配
                    if (cleanText.Equals(cleanTargetTitle, StringComparison.OrdinalIgnoreCase) ||
                        cleanText.Equals(coreTitle, StringComparison.OrdinalIgnoreCase) ||
                        (cleanText.Contains(coreTitle, StringComparison.OrdinalIgnoreCase) && coreTitle.Length > 3))
                    {
                        startIndex = i;
                        break;
                    }
                }

                // 如果没有找到标题样式，尝试匹配可能的内容标题
                if (IsLikelyContentHeading(text, styleName))
                {
                    if (cleanText.Contains(cleanTargetTitle, StringComparison.OrdinalIgnoreCase) &&
                        cleanTargetTitle.Length > 5)
                    {
                        startIndex = i;
                        break;
                    }
                }
            }

            if (startIndex == -1)
                return (-1, -1, relatedTables);

            // 查找结束位置 - 更精确的边界检测
            var startLevel = GetParagraphHeadingLevel(paragraphs[startIndex]);
            var startText = GetParagraphText(paragraphs[startIndex]).ToLower();
            var startStyleName = GetParagraphStyleName(paragraphs[startIndex]);

            // 特殊处理：对于"基本信息表"，提取标题和紧邻的表格
            if (startText.Contains("基本信息") && startText.Contains("表"))
            {
                endIndex = startIndex + 1;
                // 但仍然需要查找相关表格
            }
            else
            {
                // 对于正文标题，查找实际的章节内容
                for (int i = startIndex + 1; i < paragraphs.Count; i++)
                {
                    var text = GetParagraphText(paragraphs[i]);
                    var styleName = GetParagraphStyleName(paragraphs[i]);
                    var currentLevel = GetParagraphHeadingLevel(paragraphs[i]);

                    // 跳过目录样式的段落
                    if (IsTocStyle(styleName))
                        continue;

                    // 如果遇到同级或更高级的正文标题，则结束
                    if ((IsHeadingStyle(styleName) || IsLikelyContentHeading(text, styleName)) &&
                        currentLevel > 0 && currentLevel <= startLevel)
                    {
                        endIndex = i;
                        break;
                    }

                    // 如果遇到明确的章节分界，则结束
                    if (!string.IsNullOrWhiteSpace(text))
                    {
                        var cleanText = CleanTitle(text);

                        // 检查是否为明确的新章节
                        if (cleanText.Equals("目录", StringComparison.OrdinalIgnoreCase) ||
                            cleanText.Equals("声明", StringComparison.OrdinalIgnoreCase) ||
                            cleanText.StartsWith("附录", StringComparison.OrdinalIgnoreCase) ||
                            (IsLikelyContentHeading(cleanText, styleName) && cleanText.Length > 5))
                        {
                            endIndex = i;
                            break;
                        }
                    }
                }

                // 如果没有找到结束位置，使用更大的范围来确保包含实际内容
                if (endIndex == -1)
                {
                    // 根据标题级别决定范围，确保包含足够的内容
                    var range = startLevel <= 1 ? 100 : (startLevel == 2 ? 50 : 30);
                    endIndex = Math.Min(startIndex + range, paragraphs.Count);
                }
            }

            // 查找相关表格 - 更精确的表格关联
            relatedTables = FindRelatedTables(body, startIndex, endIndex);

            return (startIndex, endIndex, relatedTables);
        }

        private string CleanTitle(string title)
        {
            if (string.IsNullOrWhiteSpace(title))
                return string.Empty;

            // 移除Word内部引用和格式标记
            var cleaned = title;

            // 移除PAGEREF引用
            cleaned = Regex.Replace(cleaned, @"PAGEREF\s+\w+\s+\\h", "", RegexOptions.IgnoreCase);

            // 移除其他Word字段代码
            cleaned = Regex.Replace(cleaned, @"\\[a-zA-Z]+\s*", "");

            // 移除多余的空格和特殊字符
            cleaned = Regex.Replace(cleaned, @"\s+", " ");
            cleaned = cleaned.Trim();

            // 移除页码引用
            cleaned = Regex.Replace(cleaned, @"\s+\d+\s*$", "");

            return cleaned;
        }

        private int GetParagraphHeadingLevel(Paragraph paragraph)
        {
            var styleName = GetParagraphStyleName(paragraph);
            return GetHeadingLevel(styleName);
        }

        private bool IsLikelyNewSection(string text)
        {
            // 检查是否像是新章节的开始
            var patterns = new[]
            {
                @"^\d+\s+\w+",           // 数字开头的标题
                @"^第\w+章",             // 第X章
                @"^第\w+节",             // 第X节
                @"^\w+概述$",            // XX概述
                @"^\w+说明$",            // XX说明
                @"^\w+分析$"             // XX分析
            };

            return patterns.Any(pattern => Regex.IsMatch(text, pattern));
        }

        private List<int> FindRelatedTables(Body body, int startIndex, int endIndex)
        {
            var relatedTables = new List<int>();
            var allElements = body.Elements().ToList();
            var paragraphs = body.Elements<Paragraph>().ToList();
            var tables = body.Elements<Table>().ToList();

            // 特殊处理：对于"基本信息表"这类标题，只包含紧邻的表格
            if (startIndex >= 0 && startIndex < paragraphs.Count)
            {
                var titleText = GetParagraphText(paragraphs[startIndex]).ToLower();
                if (titleText.Contains("基本信息") && titleText.Contains("表"))
                {
                    // 对于基本信息表，只包含第一个表格（最相关的）
                    if (tables.Count > 0)
                    {
                        // 查找标题后最近的表格
                        var titleElementIndex = -1;
                        for (int i = 0; i < allElements.Count; i++)
                        {
                            if (allElements[i] is Paragraph para && paragraphs.IndexOf(para) == startIndex)
                            {
                                titleElementIndex = i;
                                break;
                            }
                        }

                        if (titleElementIndex != -1)
                        {
                            // 查找标题后的第一个表格
                            for (int i = titleElementIndex + 1; i < Math.Min(titleElementIndex + 10, allElements.Count); i++)
                            {
                                if (allElements[i] is Table table)
                                {
                                    var tableIndex = tables.IndexOf(table);
                                    relatedTables.Add(tableIndex);
                                    break; // 只要第一个表格
                                }
                            }
                        }
                    }
                    return relatedTables;
                }
            }

            // 找到开始和结束段落在所有元素中的位置
            int startElementIndex = -1;
            int endElementIndex = -1;

            for (int i = 0; i < allElements.Count; i++)
            {
                if (allElements[i] is Paragraph para)
                {
                    var paraIndex = paragraphs.IndexOf(para);
                    if (paraIndex == startIndex && startElementIndex == -1)
                        startElementIndex = i;
                    if (paraIndex == endIndex - 1)
                        endElementIndex = i;
                }
            }

            // 查找在范围内的表格
            for (int i = 0; i < allElements.Count; i++)
            {
                if (allElements[i] is Table table)
                {
                    var tableIndex = tables.IndexOf(table);

                    // 如果表格在我们的范围内
                    if (startElementIndex != -1 && endElementIndex != -1 &&
                        i >= startElementIndex && i <= endElementIndex)
                    {
                        relatedTables.Add(tableIndex);
                    }
                }
            }

            return relatedTables;
        }

        private void CopyDocumentStructure(WordprocessingDocument source, WordprocessingDocument target)
        {
            // 创建主文档部分
            var mainPart = target.AddMainDocumentPart();
            mainPart.Document = new Document(new Body());

            // 复制样式
            if (source.MainDocumentPart?.StyleDefinitionsPart != null)
            {
                var stylesPart = mainPart.AddNewPart<StyleDefinitionsPart>();
                stylesPart.Styles = (Styles)source.MainDocumentPart.StyleDefinitionsPart.Styles.CloneNode(true);
            }

            // 复制字体表
            if (source.MainDocumentPart?.FontTablePart != null)
            {
                var fontTablePart = mainPart.AddNewPart<FontTablePart>();
                fontTablePart.Fonts = (Fonts)source.MainDocumentPart.FontTablePart.Fonts.CloneNode(true);
            }

            // 复制主题
            if (source.MainDocumentPart?.ThemePart != null)
            {
                var themePart = mainPart.AddNewPart<ThemePart>();
                using var themeStream = source.MainDocumentPart.ThemePart.GetStream();
                themePart.FeedData(themeStream);
            }
        }

        private void CopySectionWithFormat(Body sourceBody, Body targetBody, int startIndex, int endIndex, List<int> tableIndices)
        {
            var sourceParagraphs = sourceBody.Elements<Paragraph>().ToList();
            var sourceTables = sourceBody.Elements<Table>().ToList();

            // 复制段落，保持原始格式
            for (int i = startIndex; i < endIndex && i < sourceParagraphs.Count; i++)
            {
                var sourceParagraph = sourceParagraphs[i];
                var text = GetParagraphText(sourceParagraph);

                // 跳过空段落
                if (string.IsNullOrWhiteSpace(text))
                    continue;

                // 深度克隆段落以保持所有格式
                var clonedParagraph = (Paragraph)sourceParagraph.CloneNode(true);
                targetBody.AppendChild(clonedParagraph);
            }

            // 复制相关表格，保持原始格式
            foreach (var tableIndex in tableIndices)
            {
                if (tableIndex < sourceTables.Count)
                {
                    var sourceTable = sourceTables[tableIndex];
                    // 深度克隆表格以保持所有格式、样式和结构
                    var clonedTable = (Table)sourceTable.CloneNode(true);
                    targetBody.AppendChild(clonedTable);
                }
            }
        }
    }
}
