#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板替换性能优化效果
验证大量标签时的处理速度
"""

import requests
import time
import os
from docx import Document

def create_large_template():
    """
    创建包含大量标签的测试模板
    """
    print("📝 创建包含大量标签的测试模板")
    
    doc = Document()
    
    # 添加标题
    doc.add_heading('大量标签性能测试模板', 0)
    
    # 添加说明
    doc.add_paragraph('本文档包含大量标签，用于测试模板替换的性能优化效果。')
    
    # 创建多个标签
    tags = [
        '{测评项目概述}',
        '{测评项目概述-测评目的}',
        '{测评项目概述-测评依据}',
        '{声明}',
        '{等级测评结论}',
        '{重大风险隐患及整改建议}',
        '{网络安全等级测评基本信息表}',
        '{被测对象描述}',
        '{测评项目概述}',  # 重复标签测试去重
        '{测评项目概述-测评目的}',  # 重复标签测试去重
    ]
    
    # 添加多个章节，每个章节包含多个标签
    for section in range(1, 6):  # 5个章节
        doc.add_heading(f'第{section}章 测试章节', level=1)
        
        for subsection in range(1, 4):  # 每章3个小节
            doc.add_heading(f'{section}.{subsection} 测试小节', level=2)
            
            # 每个小节添加多个标签
            for tag_idx, tag in enumerate(tags):
                doc.add_paragraph(f'标签{tag_idx + 1}：{tag}')
                
                # 添加一些混合内容
                if tag_idx % 2 == 0:
                    doc.add_paragraph(f'这是包含标签的段落：根据{tag}，我们可以得出相关结论。')
    
    # 添加表格测试
    doc.add_heading('表格中的标签测试', level=1)
    table = doc.add_table(rows=len(tags) + 1, cols=3)
    table.style = 'Table Grid'
    
    # 表格标题行
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text = '序号'
    hdr_cells[1].text = '标签'
    hdr_cells[2].text = '说明'
    
    # 表格内容行
    for i, tag in enumerate(tags):
        row_cells = table.rows[i + 1].cells
        row_cells[0].text = str(i + 1)
        row_cells[1].text = tag
        row_cells[2].text = f'这是{tag}的说明'
    
    # 保存文档
    filename = '大量标签测试模板.docx'
    doc.save(filename)
    
    # 统计标签数量
    total_tags = len(tags) * 5 * 3 + len(tags) * 2 + len(tags)  # 段落标签 + 混合内容标签 + 表格标签
    unique_tags = len(set(tags))
    
    print(f"✅ 大量标签测试模板已创建: {filename}")
    print(f"📊 标签统计:")
    print(f"   总标签数: {total_tags}")
    print(f"   唯一标签数: {unique_tags}")
    print(f"   章节数: 5")
    print(f"   小节数: 15")
    print(f"   表格行数: {len(tags) + 1}")
    
    return filename, total_tags, unique_tags

def test_performance(template_file, total_tags, unique_tags):
    """
    测试模板替换性能
    """
    print(f"\n⏱️ 性能测试开始")
    print("="*50)
    
    reference_file = "测评报告.docx"
    
    if not os.path.exists(reference_file):
        print(f"❌ 参考文件不存在: {reference_file}")
        return
    
    print(f"📋 测试配置:")
    print(f"   模板文件: {template_file}")
    print(f"   参考文件: {reference_file}")
    print(f"   总标签数: {total_tags}")
    print(f"   唯一标签数: {unique_tags}")
    
    # 记录开始时间
    start_time = time.time()
    
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        print(f"\n🚀 开始模板替换...")
        
        with open(template_file, 'rb') as tf, open(reference_file, 'rb') as rf:
            files = {
                'templateFile': tf,
                'referenceFile': rf
            }
            response = requests.post(f"{base_url}/replace-template", files=files)
        
        # 记录结束时间
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            # 保存结果文件
            output_file = "性能测试结果.docx"
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 模板替换成功: {output_file}")
            
            # 分析性能结果
            analyze_performance_result(output_file, processing_time, total_tags, unique_tags)
            
        else:
            print(f"❌ 模板替换失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except Exception as e:
        end_time = time.time()
        processing_time = end_time - start_time
        print(f"❌ 模板替换出错: {str(e)}")
        print(f"⏱️ 失败前耗时: {processing_time:.2f} 秒")

def analyze_performance_result(output_file, processing_time, total_tags, unique_tags):
    """
    分析性能测试结果
    """
    print(f"\n📊 性能分析结果:")
    print("-" * 40)
    
    try:
        doc = Document(output_file)
        file_size = os.path.getsize(output_file)
        
        print(f"⏱️ 处理时间: {processing_time:.2f} 秒")
        print(f"📁 输出文件大小: {file_size:,} 字节")
        print(f"📄 输出段落数: {len(doc.paragraphs)}")
        print(f"📊 输出表格数: {len(doc.tables)}")
        
        # 计算性能指标
        tags_per_second = total_tags / processing_time if processing_time > 0 else 0
        unique_tags_per_second = unique_tags / processing_time if processing_time > 0 else 0
        
        print(f"\n📈 性能指标:")
        print(f"   总标签处理速度: {tags_per_second:.1f} 标签/秒")
        print(f"   唯一标签处理速度: {unique_tags_per_second:.1f} 标签/秒")
        print(f"   平均每标签耗时: {(processing_time / total_tags * 1000):.1f} 毫秒")
        
        # 性能评估
        if processing_time < 10:
            performance_rating = "🚀 优秀"
        elif processing_time < 30:
            performance_rating = "✅ 良好"
        elif processing_time < 60:
            performance_rating = "⚠️ 一般"
        else:
            performance_rating = "❌ 需要优化"
        
        print(f"   性能评级: {performance_rating}")
        
        # 检查内容质量
        all_text = " ".join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
        remaining_tags = all_text.count('{')
        
        print(f"\n🎯 替换质量:")
        print(f"   剩余未替换标签: {remaining_tags}")
        if remaining_tags == 0:
            print(f"   替换完成度: ✅ 100%")
        else:
            completion_rate = ((total_tags - remaining_tags) / total_tags) * 100
            print(f"   替换完成度: {completion_rate:.1f}%")
        
        # 优化效果分析
        print(f"\n💡 优化效果分析:")
        estimated_old_time = unique_tags * 2  # 假设优化前每个唯一标签需要2秒
        if processing_time < estimated_old_time:
            speedup = estimated_old_time / processing_time
            print(f"   预估优化前耗时: {estimated_old_time:.1f} 秒")
            print(f"   性能提升倍数: {speedup:.1f}x")
            print(f"   时间节省: {estimated_old_time - processing_time:.1f} 秒")
        else:
            print(f"   当前性能已经很好，无需进一步优化")
        
    except Exception as e:
        print(f"❌ 分析结果时出错: {str(e)}")

def test_simple_template_for_comparison():
    """
    测试简单模板作为对比
    """
    print(f"\n🔄 对比测试：简单模板")
    print("-" * 40)
    
    template_file = "简单模板.docx"
    reference_file = "测评报告.docx"
    
    if not os.path.exists(template_file) or not os.path.exists(reference_file):
        print(f"❌ 对比测试文件不存在")
        return
    
    start_time = time.time()
    
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        with open(template_file, 'rb') as tf, open(reference_file, 'rb') as rf:
            files = {
                'templateFile': tf,
                'referenceFile': rf
            }
            response = requests.post(f"{base_url}/replace-template", files=files)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            print(f"✅ 简单模板替换成功")
            print(f"⏱️ 处理时间: {processing_time:.2f} 秒")
            print(f"📊 简单模板标签数: 3")
            print(f"📈 简单模板处理速度: {3 / processing_time:.1f} 标签/秒")
        else:
            print(f"❌ 简单模板替换失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 对比测试出错: {str(e)}")

def main():
    """
    主测试函数
    """
    print("🚀 模板替换性能优化测试")
    print("="*60)
    
    # 创建大量标签的测试模板
    template_file, total_tags, unique_tags = create_large_template()
    
    # 性能测试
    test_performance(template_file, total_tags, unique_tags)
    
    # 对比测试
    test_simple_template_for_comparison()
    
    print(f"\n💡 性能优化要点:")
    print(f"1. 预提取所有唯一标签的内容，避免重复处理")
    print(f"2. 批量处理，减少文档操作次数")
    print(f"3. 内存优化，减少不必要的文档克隆")
    print(f"4. 智能缓存，相同标签复用提取结果")
    
    print(f"\n🎯 如果性能仍不理想，可以考虑:")
    print(f"1. 异步并行处理多个标签")
    print(f"2. 流式处理大文档")
    print(f"3. 增加进度反馈机制")
    print(f"4. 实现标签内容缓存")

if __name__ == "__main__":
    main()
