#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档内容提取演示脚本
展示如何使用Python版本提取"网络安全等级测评基本信息表"内容
"""

import os
import sys
from docx import Document
import json

def demo_extract_basic_info_table():
    """
    演示提取"网络安全等级测评基本信息表"
    """
    print("🎯 Word文档内容提取演示")
    print("="*60)
    
    # 检查源文档是否存在
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return False
    
    print(f"📖 源文档: {source_file}")
    
    try:
        # 打开文档
        doc = Document(source_file)
        
        # 查找目标标题
        target_title = "网络安全等级测评基本信息表"
        print(f"🎯 目标标题: {target_title}")
        
        # 查找标题位置
        start_index = None
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if target_title in text:
                start_index = i
                print(f"✅ 找到标题位置: 段落 {i}")
                break
        
        if start_index is None:
            print(f"❌ 未找到标题: {target_title}")
            return False
        
        # 创建新文档
        new_doc = Document()
        
        # 复制标题
        title_para = new_doc.add_paragraph()
        title_para.text = target_title
        title_para.style = 'Title'
        
        print("📋 正在复制相关表格...")
        
        # 复制前几个表格（通常包含基本信息）
        table_count = 0
        for i, table in enumerate(doc.tables):
            if i < 3:  # 只复制前3个表格
                # 创建新表格
                new_table = new_doc.add_table(rows=len(table.rows), cols=len(table.columns))
                
                # 复制表格内容
                for row_idx, row in enumerate(table.rows):
                    for col_idx, cell in enumerate(row.cells):
                        new_table.cell(row_idx, col_idx).text = cell.text
                
                table_count += 1
                print(f"  ✅ 已复制表格 {i+1}")
        
        # 保存新文档
        output_file = f"{target_title}_提取结果.docx"
        new_doc.save(output_file)
        
        print(f"\n🎉 提取完成!")
        print(f"📄 输出文件: {output_file}")
        print(f"📊 复制了 {table_count} 个表格")
        
        # 验证结果
        verify_extracted_document(output_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 提取过程中出错: {str(e)}")
        return False

def verify_extracted_document(file_path):
    """
    验证提取的文档
    """
    print(f"\n🔍 验证提取结果...")
    
    try:
        doc = Document(file_path)
        
        print(f"📄 段落数: {len(doc.paragraphs)}")
        print(f"📊 表格数: {len(doc.tables)}")
        
        # 显示段落内容
        print(f"\n📝 段落内容:")
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                print(f"  {i+1}. {text}")
        
        # 显示表格信息
        if doc.tables:
            print(f"\n📊 表格信息:")
            for i, table in enumerate(doc.tables):
                print(f"  表格 {i+1}: {len(table.rows)} 行 x {len(table.columns)} 列")
                
                # 显示表格的第一行内容作为示例
                if table.rows:
                    first_row = table.rows[0]
                    row_text = []
                    for cell in first_row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text[:15] + ('...' if len(cell_text) > 15 else ''))
                    if row_text:
                        print(f"    示例行: {' | '.join(row_text)}")
        
        print("✅ 验证完成!")
        
    except Exception as e:
        print(f"❌ 验证时出错: {str(e)}")

def show_available_headings():
    """
    显示文档中可用的标题
    """
    print("\n📋 文档中的可用标题:")
    print("-" * 40)
    
    # 读取之前的分析结果
    analysis_file = "测评报告_toc_analysis.json"
    if os.path.exists(analysis_file):
        try:
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis = json.load(f)
            
            print("🎯 目录中的标题 (前20个):")
            for i, entry in enumerate(analysis['toc_headings'][:20]):
                level_indent = "  " * (entry['level'] - 1)
                print(f"{level_indent}• {entry['title']} (第{entry['page_number']}页)")
            
            if len(analysis['toc_headings']) > 20:
                print(f"  ... 还有 {len(analysis['toc_headings']) - 20} 个标题")
                
        except Exception as e:
            print(f"❌ 读取分析结果时出错: {str(e)}")
    else:
        print("❌ 未找到文档分析结果，请先运行 extract_toc_headings.py")

def main():
    """
    主演示函数
    """
    print("🚀 Word文档内容提取工具演示")
    print("="*60)
    print()
    
    # 显示可用标题
    show_available_headings()
    
    print("\n" + "="*60)
    print("开始提取演示...")
    print("="*60)
    
    # 演示提取基本信息表
    success = demo_extract_basic_info_table()
    
    if success:
        print(f"\n🎉 演示完成!")
        print(f"💡 提示:")
        print(f"  - 可以修改 target_title 变量来提取其他标题的内容")
        print(f"  - 生成的文档保留了基本格式和表格结构")
        print(f"  - 如需完全保留格式，建议使用C# Web版本")
    else:
        print(f"\n❌ 演示失败!")
        print(f"💡 建议:")
        print(f"  - 检查源文档是否存在")
        print(f"  - 确认标题名称是否正确")
        print(f"  - 查看错误信息进行调试")

if __name__ == "__main__":
    main()
