<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档模板替换工具</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .upload-area.has-file {
            border-color: #198754;
            background-color: #d1e7dd;
        }
        .loading {
            display: none;
        }
        .file-info {
            background-color: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            margin-top: 10px;
        }
        .process-step {
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #0d6efd;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .step-completed .step-number {
            background-color: #198754;
        }
        .result-section {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">
                    <i class="fas fa-file-word text-primary"></i>
                    Word文档模板替换工具
                </h1>
                
                <div class="alert alert-info">
                    <h5><i class="fas fa-info-circle"></i> 功能说明</h5>
                    <p class="mb-2">本工具可以将模板文档中的标签（如 <code>{测评项目概述}</code> 或 <code>{测评项目概述-测评目的}</code>）替换为参考文档中对应标题的内容。</p>
                    <ul class="mb-0">
                        <li>支持标签格式：<code>{标题}</code> 或 <code>{父标题-子标题}</code></li>
                        <li>完整保留原始格式：段落样式、表格结构、图片等</li>
                        <li>自动匹配标题并替换为对应内容</li>
                    </ul>
                </div>

                <!-- 步骤1: 上传模板文档 -->
                <div class="process-step border" id="step1">
                    <div class="d-flex align-items-center mb-3">
                        <div class="step-number">1</div>
                        <h4 class="mb-0">上传模板文档</h4>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="upload-area" id="templateUploadArea">
                                <i class="fas fa-file-upload fa-3x text-muted mb-3"></i>
                                <p class="mb-2">选择包含标签的模板文档</p>
                                <p class="text-muted small">支持 .docx 和 .doc 格式</p>
                                <input type="file" id="templateFileInput" accept=".docx,.doc" style="display: none;">
                            </div>
                            <div id="templateFileInfo" class="file-info" style="display: none;">
                                <i class="fas fa-file-word text-primary"></i>
                                <span id="templateFileName"></span>
                                <span class="badge bg-success ms-2">已选择</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 步骤2: 上传参考文档 -->
                <div class="process-step border" id="step2">
                    <div class="d-flex align-items-center mb-3">
                        <div class="step-number">2</div>
                        <h4 class="mb-0">上传参考文档</h4>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="upload-area" id="referenceUploadArea">
                                <i class="fas fa-file-import fa-3x text-muted mb-3"></i>
                                <p class="mb-2">选择包含实际内容的参考文档</p>
                                <p class="text-muted small">支持 .docx 和 .doc 格式</p>
                                <input type="file" id="referenceFileInput" accept=".docx,.doc" style="display: none;">
                            </div>
                            <div id="referenceFileInfo" class="file-info" style="display: none;">
                                <i class="fas fa-file-word text-primary"></i>
                                <span id="referenceFileName"></span>
                                <span class="badge bg-success ms-2">已选择</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 步骤3: 执行替换 -->
                <div class="process-step border" id="step3">
                    <div class="d-flex align-items-center mb-3">
                        <div class="step-number">3</div>
                        <h4 class="mb-0">执行模板替换</h4>
                    </div>
                    <div class="card">
                        <div class="card-body text-center">
                            <button class="btn btn-primary btn-lg" id="replaceBtn" disabled>
                                <i class="fas fa-magic"></i> 开始替换
                            </button>
                            <div class="loading mt-3">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">处理中...</span>
                                </div>
                                <p class="mt-2">正在处理模板替换，请稍候...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 替换结果 -->
                <div class="result-section" id="resultSection">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-check-circle"></i> 替换完成</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-thumbs-up"></i> 模板替换成功！</h6>
                                <p class="mb-0">已将模板中的标签替换为参考文档中的对应内容，并保留了原始格式。</p>
                            </div>
                            <div id="resultInfo">
                                <!-- 结果信息将在这里显示 -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 错误信息 -->
                <div class="alert alert-danger" id="errorAlert" style="display: none;">
                    <h6><i class="fas fa-exclamation-triangle"></i> 处理失败</h6>
                    <p id="errorMessage" class="mb-0"></p>
                </div>

                <!-- 返回链接 -->
                <div class="text-center mt-4">
                    <a href="index.html" class="btn btn-outline-primary">
                        <i class="fas fa-arrow-left"></i> 返回内容提取工具
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="template.js"></script>
</body>
</html>
