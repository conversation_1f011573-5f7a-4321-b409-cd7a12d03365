using WordExtractor.Models;

namespace WordExtractor.Services
{
    public interface IWordExtractionService
    {
        /// <summary>
        /// 分析Word文档并获取目录信息
        /// </summary>
        /// <param name="stream">文档流</param>
        /// <returns>文档分析结果</returns>
        Task<DocumentAnalysisResult> AnalyzeDocumentAsync(Stream stream);

        /// <summary>
        /// 提取指定标题的内容并保留原格式
        /// </summary>
        /// <param name="stream">源文档流</param>
        /// <param name="targetTitle">目标标题</param>
        /// <returns>提取的文档字节数组</returns>
        Task<byte[]> ExtractSectionWithFormatAsync(Stream stream, string targetTitle);

        /// <summary>
        /// 获取文档中的所有标题
        /// </summary>
        /// <param name="stream">文档流</param>
        /// <returns>标题列表</returns>
        Task<List<DocumentHeading>> GetDocumentHeadingsAsync(Stream stream);

        /// <summary>
        /// 替换模板文档中的标签为参考文档中的对应内容
        /// </summary>
        /// <param name="templateStream">模板文档流</param>
        /// <param name="referenceStream">参考文档流</param>
        /// <returns>替换后的文档字节数组</returns>
        Task<byte[]> ReplaceTemplateTagsAsync(Stream templateStream, Stream referenceStream);
    }
}
