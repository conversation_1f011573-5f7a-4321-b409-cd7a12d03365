{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "app.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000201126307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "ETag", "Value": "W/\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "21340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:09 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk="}]}, {"Route": "app.v20ta0p5ky.js", "AssetFile": "app.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000201126307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "ETag", "Value": "W/\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.v20ta0p5ky.js", "AssetFile": "app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "21340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:09 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}, {"Name": "label", "Value": "app.js"}]}, {"Route": "app.v20ta0p5ky.js.gz", "AssetFile": "app.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "integrity", "Value": "sha256-+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk="}, {"Name": "label", "Value": "app.js.gz"}]}, {"Route": "index.f31xul4g0z.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000505305710"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=\""}, {"Name": "ETag", "Value": "W/\"NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f31xul4g0z"}, {"Name": "integrity", "Value": "sha256-NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.f31xul4g0z.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8604"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:57 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f31xul4g0z"}, {"Name": "integrity", "Value": "sha256-NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o="}, {"Name": "label", "Value": "index.html"}]}, {"Route": "index.f31xul4g0z.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f31xul4g0z"}, {"Name": "integrity", "Value": "sha256-Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls="}, {"Name": "label", "Value": "index.html.gz"}]}, {"Route": "index.html", "AssetFile": "index.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000505305710"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=\""}, {"Name": "ETag", "Value": "W/\"NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o="}]}, {"Route": "index.html", "AssetFile": "index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8604"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:57 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o="}]}, {"Route": "index.html.gz", "AssetFile": "index.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls="}]}, {"Route": "template.7q71xfk5fx.html", "AssetFile": "template.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000468164794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2135"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=\""}, {"Name": "ETag", "Value": "W/\"X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7q71xfk5fx"}, {"Name": "integrity", "Value": "sha256-X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno="}, {"Name": "label", "Value": "template.html"}]}, {"Route": "template.7q71xfk5fx.html", "AssetFile": "template.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8650"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:07 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7q71xfk5fx"}, {"Name": "integrity", "Value": "sha256-X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno="}, {"Name": "label", "Value": "template.html"}]}, {"Route": "template.7q71xfk5fx.html.gz", "AssetFile": "template.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2135"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7q71xfk5fx"}, {"Name": "integrity", "Value": "sha256-SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA="}, {"Name": "label", "Value": "template.html.gz"}]}, {"Route": "template.g0zvhtklle.js", "AssetFile": "template.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000403225806"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=\""}, {"Name": "ETag", "Value": "W/\"AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0zvhtklle"}, {"Name": "integrity", "Value": "sha256-AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM="}, {"Name": "label", "Value": "template.js"}]}, {"Route": "template.g0zvhtklle.js", "AssetFile": "template.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "8614"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:44 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0zvhtklle"}, {"Name": "integrity", "Value": "sha256-AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM="}, {"Name": "label", "Value": "template.js"}]}, {"Route": "template.g0zvhtklle.js.gz", "AssetFile": "template.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0zvhtklle"}, {"Name": "integrity", "Value": "sha256-UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU="}, {"Name": "label", "Value": "template.js.gz"}]}, {"Route": "template.html", "AssetFile": "template.html.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000468164794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2135"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=\""}, {"Name": "ETag", "Value": "W/\"X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno="}]}, {"Route": "template.html", "AssetFile": "template.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8650"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:07 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno="}]}, {"Route": "template.html.gz", "AssetFile": "template.html.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2135"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA="}]}, {"Route": "template.js", "AssetFile": "template.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000403225806"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=\""}, {"Name": "ETag", "Value": "W/\"AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM="}]}, {"Route": "template.js", "AssetFile": "template.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "8614"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:44 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM="}]}, {"Route": "template.js.gz", "AssetFile": "template.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU="}]}]}