// 标签导出功能的JavaScript代码

let selectedFile = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeTagExport();
});

function initializeTagExport() {
    // 初始化文件上传
    setupFileUpload();
    
    // 初始化分析按钮
    document.getElementById('analyzeBtn').addEventListener('click', analyzeDocument);
}

function setupFileUpload() {
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    
    // 点击上传区域
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });
    
    // 文件选择
    fileInput.addEventListener('change', handleFileSelect);
    
    // 拖拽功能
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });
    
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });
    
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect({ target: { files: files } });
        }
    });
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        if (isWordDocument(file.name)) {
            selectedFile = file;
            showFileInfo(file.name);
            hideResults();
        } else {
            showError('请选择Word文档文件 (.docx 或 .doc)');
        }
    }
}

function showFileInfo(fileName) {
    const uploadArea = document.getElementById('uploadArea');
    const fileInfo = document.getElementById('fileInfo');
    const fileNameElement = document.getElementById('fileName');
    
    uploadArea.classList.add('has-file');
    fileInfo.style.display = 'block';
    fileNameElement.textContent = fileName;
}

function hideResults() {
    document.getElementById('resultSection').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
}

function isWordDocument(fileName) {
    const extension = fileName.toLowerCase().split('.').pop();
    return extension === 'docx' || extension === 'doc';
}

async function analyzeDocument() {
    if (!selectedFile) {
        showError('请先选择文档文件');
        return;
    }
    
    const analyzeBtn = document.getElementById('analyzeBtn');
    const loading = document.querySelector('.loading');
    const errorAlert = document.getElementById('errorAlert');
    
    try {
        // 显示加载状态
        analyzeBtn.disabled = true;
        loading.style.display = 'block';
        errorAlert.style.display = 'none';
        hideResults();
        
        // 创建FormData
        const formData = new FormData();
        formData.append('file', selectedFile);
        
        // 发送请求
        const response = await fetch('/api/wordextraction/export-tags', {
            method: 'POST',
            body: formData
        });
        
        if (response.ok) {
            const result = await response.json();
            showResults(result);
        } else {
            const errorText = await response.text();
            throw new Error(errorText || '标题分析失败');
        }
        
    } catch (error) {
        console.error('标题分析错误:', error);
        showError(error.message || '标题分析过程中发生错误');
    } finally {
        // 隐藏加载状态
        loading.style.display = 'none';
        analyzeBtn.disabled = false;
    }
}

function showResults(result) {
    const resultSection = document.getElementById('resultSection');
    const summaryInfo = document.getElementById('summaryInfo');
    
    // 显示摘要信息
    summaryInfo.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <strong>文档名称:</strong><br>
                <span class="text-muted">${result.fileName}</span>
            </div>
            <div class="col-md-3">
                <strong>标题总数:</strong><br>
                <span class="badge bg-primary fs-6">${result.totalHeadings}</span>
            </div>
            <div class="col-md-3">
                <strong>标题级别:</strong><br>
                <span class="text-muted">${result.tagsGroupedByLevel.length} 个级别</span>
            </div>
            <div class="col-md-3">
                <strong>父子标题:</strong><br>
                <span class="text-muted">${result.parentChildTags.length} 个</span>
            </div>
        </div>
    `;
    
    // 显示简单标签格式
    document.getElementById('simpleTagsOutput').textContent = result.allTagsText;
    
    // 显示带注释的标签格式
    document.getElementById('commentedTagsOutput').textContent = result.allTagsWithComments;
    
    // 显示父子标题格式
    const parentChildText = result.parentChildTags
        .map(t => `${t.tag} <!-- ${t.childTitle} (${t.level}级标题，第${t.pageNumber}页) -->`)
        .join('\n');
    document.getElementById('parentChildTagsOutput').textContent = parentChildText || '没有找到父子标题关系';
    
    // 显示按级别分组的标签
    showGroupedTags(result.tagsGroupedByLevel);
    
    // 显示结果区域
    resultSection.style.display = 'block';
    
    // 滚动到结果区域
    resultSection.scrollIntoView({ behavior: 'smooth' });
}

function showGroupedTags(groupedTags) {
    const container = document.getElementById('groupedTagsOutput');
    
    let html = '';
    groupedTags.forEach(group => {
        html += `
            <div class="tag-group">
                <h6>
                    ${group.level}级标题 
                    <span class="badge bg-secondary level-badge">${group.count}个</span>
                </h6>
                <div class="tag-output" style="max-height: 150px;">
${group.tags.join('\n')}
                </div>
                <button class="btn btn-outline-primary btn-sm mt-2 copy-btn" onclick="copyGroupTags(${group.level}, this)">
                    <i class="fas fa-copy"></i> 复制${group.level}级标题
                    <div class="copy-success">已复制!</div>
                </button>
            </div>
        `;
    });
    
    container.innerHTML = html;
}

function copyToClipboard(elementId, button) {
    const element = document.getElementById(elementId);
    const text = element.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
        showCopySuccess(button);
    }).catch(err => {
        console.error('复制失败:', err);
        // 降级方案
        fallbackCopyTextToClipboard(text, button);
    });
}

function copyGroupTags(level, button) {
    const groupContainer = button.parentElement;
    const tagOutput = groupContainer.querySelector('.tag-output');
    const text = tagOutput.textContent;
    
    navigator.clipboard.writeText(text).then(() => {
        showCopySuccess(button);
    }).catch(err => {
        console.error('复制失败:', err);
        fallbackCopyTextToClipboard(text, button);
    });
}

function fallbackCopyTextToClipboard(text, button) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    
    // 避免滚动到底部
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess(button);
        } else {
            console.error('降级复制方案也失败了');
        }
    } catch (err) {
        console.error('降级复制方案出错:', err);
    }
    
    document.body.removeChild(textArea);
}

function showCopySuccess(button) {
    const successElement = button.querySelector('.copy-success');
    successElement.classList.add('show');
    
    setTimeout(() => {
        successElement.classList.remove('show');
    }, 2000);
}

function showError(message) {
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');
    
    errorMessage.textContent = message;
    errorAlert.style.display = 'block';
    
    // 滚动到错误信息
    errorAlert.scrollIntoView({ behavior: 'smooth' });
    
    // 5秒后自动隐藏错误信息
    setTimeout(() => {
        errorAlert.style.display = 'none';
    }, 5000);
}
