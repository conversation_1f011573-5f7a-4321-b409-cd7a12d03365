#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试内容边界问题 - 验证是否提取了过多内容
"""

import requests
import os
from docx import Document

def test_content_boundary():
    """
    测试内容边界是否准确
    """
    print("🔍 测试内容边界准确性")
    print("="*50)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return
    
    # 测试几个标题，检查是否包含了其他标题的内容
    test_cases = [
        {
            "title": "1.1 测评目的",
            "should_not_contain": ["1.2", "测评依据", "1.3", "测评范围"],
            "expected_keywords": ["测评目的", "目的", "安全等级"]
        },
        {
            "title": "1.2 测评依据", 
            "should_not_contain": ["1.1", "测评目的", "1.3", "测评范围"],
            "expected_keywords": ["测评依据", "依据", "标准", "GB"]
        },
        {
            "title": "声明",
            "should_not_contain": ["等级测评结论", "重大风险", "目录"],
            "expected_keywords": ["声明", "报告", "测评结论", "有效性"]
        },
        {
            "title": "等级测评结论",
            "should_not_contain": ["声明", "重大风险隐患", "扩展表"],
            "expected_keywords": ["等级测评结论", "结论"]
        }
    ]
    
    base_url = "http://localhost:5000/api/wordextraction"
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}: {test_case['title']}")
        print("-" * 40)
        
        try:
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': test_case['title'],
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存文件
                safe_title = test_case['title'].replace('/', '_').replace('\\', '_').replace(':', '_').replace('.', '_')
                output_file = f"边界测试_{safe_title}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                # 分析内容
                doc = Document(output_file)
                all_text = " ".join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
                
                print(f"✅ 提取成功: {output_file}")
                print(f"📄 段落数: {len(doc.paragraphs)}")
                print(f"📊 表格数: {len(doc.tables)}")
                print(f"📝 内容长度: {len(all_text)} 字符")
                
                # 检查是否包含期望的关键词
                found_expected = []
                for keyword in test_case['expected_keywords']:
                    if keyword in all_text:
                        found_expected.append(keyword)
                
                print(f"✅ 包含期望关键词: {found_expected}")
                
                # 检查是否包含不应该包含的内容
                found_unwanted = []
                for unwanted in test_case['should_not_contain']:
                    if unwanted in all_text:
                        found_unwanted.append(unwanted)
                
                if found_unwanted:
                    print(f"❌ 包含不应该的内容: {found_unwanted}")
                    print(f"⚠️ 边界检测不准确，提取了其他标题的内容")
                else:
                    print(f"✅ 边界检测准确，未包含其他标题内容")
                
                # 显示内容预览
                preview = all_text[:300] + ("..." if len(all_text) > 300 else "")
                print(f"📖 内容预览: {preview}")
                
                # 分析段落内容，查找可能的边界问题
                print(f"\n📄 段落分析:")
                for j, para in enumerate(doc.paragraphs[:10]):  # 只显示前10个段落
                    text = para.text.strip()
                    if text:
                        # 检查是否是其他标题
                        is_other_title = any(unwanted in text for unwanted in test_case['should_not_contain'])
                        marker = " ⚠️ 可能是其他标题" if is_other_title else ""
                        print(f"   段落 {j+1}: {text[:80]}{'...' if len(text) > 80 else ''}{marker}")
                
            else:
                print(f"❌ 提取失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except Exception as e:
            print(f"❌ 测试出错: {str(e)}")

def analyze_source_document_structure():
    """
    分析源文档结构，了解标题之间的关系
    """
    print(f"\n📊 分析源文档结构")
    print("="*50)
    
    source_file = "测评报告.docx"
    try:
        doc = Document(source_file)
        
        # 查找标题段落
        headings = []
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            style_name = para.style.name if para.style else "Normal"
            
            # 检查是否是标题
            if ('heading' in style_name.lower() or 
                any(keyword in text for keyword in ["1.1", "1.2", "1.3", "声明", "等级测评结论", "重大风险"])):
                headings.append({
                    'index': i,
                    'text': text,
                    'style': style_name
                })
        
        print(f"🔍 找到 {len(headings)} 个标题:")
        for heading in headings[:15]:  # 显示前15个
            print(f"   段落 {heading['index']:3d}: {heading['text']} (样式: {heading['style']})")
        
        # 分析标题之间的距离
        print(f"\n📏 标题间距分析:")
        for i in range(len(headings) - 1):
            current = headings[i]
            next_heading = headings[i + 1]
            distance = next_heading['index'] - current['index']
            print(f"   {current['text'][:30]}... → {next_heading['text'][:30]}... : {distance} 段落")
            
    except Exception as e:
        print(f"❌ 分析源文档时出错: {str(e)}")

def main():
    """
    主测试函数
    """
    print("🔧 内容边界准确性测试")
    print("="*60)
    
    # 测试内容边界
    test_content_boundary()
    
    # 分析源文档结构
    analyze_source_document_structure()
    
    print(f"\n💡 如果发现边界问题，需要:")
    print(f"1. 改进标题匹配逻辑，更精确地识别下一个标题")
    print(f"2. 减少提取范围，避免包含其他标题的内容")
    print(f"3. 增强边界检测，在遇到同级或更高级标题时停止")

if __name__ == "__main__":
    main()
