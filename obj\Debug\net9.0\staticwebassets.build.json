{"Version": 1, "Hash": "nxaNMguqcmWSMTwZ+GcOo9cnhLeoBY3BE240xQR9/Ug=", "Source": "WordExtractor", "BasePath": "_content/WordExtractor", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "WordExtractor\\wwwroot", "Source": "WordExtractor", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-f31xul4g0z.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint=f31xul4g0z}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d3xy7z5skb", "Integrity": "Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "FileLength": 1978, "LastWriteTime": "2025-06-09T10:35:11+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint=v20ta0p5ky}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z259h50fpz", "Integrity": "+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "FileLength": 4971, "LastWriteTime": "2025-06-09T09:06:45+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\hyx4dqjg3v-g0zvhtklle.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "template#[.{fingerprint=g0zvhtklle}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "u93bzp51n4", "Integrity": "UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.js", "FileLength": 2479, "LastWriteTime": "2025-06-09T10:35:11+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\tbtfvwlp9r-7q71xfk5fx.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "template#[.{fingerprint=7q71xfk5fx}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bz8lz53z9k", "Integrity": "SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.html", "FileLength": 2135, "LastWriteTime": "2025-06-09T10:35:11+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v20ta0p5ky", "Integrity": "WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 21340, "LastWriteTime": "2025-06-09T09:06:09+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "f31xul4g0z", "Integrity": "NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 8604, "LastWriteTime": "2025-06-09T10:32:57+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.html", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "template#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7q71xfk5fx", "Integrity": "X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template.html", "FileLength": 8650, "LastWriteTime": "2025-06-09T10:32:07+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.js", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "template#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "g0zvhtklle", "Integrity": "AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\template.js", "FileLength": 8614, "LastWriteTime": "2025-06-09T10:32:44+00:00"}], "Endpoints": [{"Route": "app.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000201126307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk="}]}, {"Route": "app.v20ta0p5ky.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000201126307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.v20ta0p5ky.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.v20ta0p5ky.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "label", "Value": "app.js.gz"}, {"Name": "integrity", "Value": "sha256-+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk="}]}, {"Route": "index.f31xul4g0z.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-f31xul4g0z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000505305710"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "ETag", "Value": "\"Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f31xul4g0z"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o="}]}, {"Route": "index.f31xul4g0z.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8604"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f31xul4g0z"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o="}]}, {"Route": "index.f31xul4g0z.html.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-f31xul4g0z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f31xul4g0z"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-f31xul4g0z.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000505305710"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "ETag", "Value": "\"Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8604"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NeqbNnPs9NLvU8/6+8/CPE8LapRX4Lm91mH3qP5lQ9o="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-f31xul4g0z.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1978"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Cf/abFdronVpY2Skg9EBfW3gGWaKr9ek+UTdty5Ctls="}]}, {"Route": "template.7q71xfk5fx.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\tbtfvwlp9r-7q71xfk5fx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000468164794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2135"}, {"Name": "ETag", "Value": "\"SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7q71xfk5fx"}, {"Name": "label", "Value": "template.html"}, {"Name": "integrity", "Value": "sha256-X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno="}]}, {"Route": "template.7q71xfk5fx.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8650"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7q71xfk5fx"}, {"Name": "label", "Value": "template.html"}, {"Name": "integrity", "Value": "sha256-X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno="}]}, {"Route": "template.7q71xfk5fx.html.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\tbtfvwlp9r-7q71xfk5fx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2135"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7q71xfk5fx"}, {"Name": "label", "Value": "template.html.gz"}, {"Name": "integrity", "Value": "sha256-SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA="}]}, {"Route": "template.g0zvhtklle.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\hyx4dqjg3v-g0zvhtklle.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000403225806"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2479"}, {"Name": "ETag", "Value": "\"UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0zvhtklle"}, {"Name": "label", "Value": "template.js"}, {"Name": "integrity", "Value": "sha256-AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM="}]}, {"Route": "template.g0zvhtklle.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8614"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0zvhtklle"}, {"Name": "label", "Value": "template.js"}, {"Name": "integrity", "Value": "sha256-AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM="}]}, {"Route": "template.g0zvhtklle.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\hyx4dqjg3v-g0zvhtklle.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "g0zvhtklle"}, {"Name": "label", "Value": "template.js.gz"}, {"Name": "integrity", "Value": "sha256-UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU="}]}, {"Route": "template.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\tbtfvwlp9r-7q71xfk5fx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000468164794"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2135"}, {"Name": "ETag", "Value": "\"SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno="}]}, {"Route": "template.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8650"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-X7rVXVmynjwne+YKmkiwNp9L37siX5ZjkXEy8ZgeCno="}]}, {"Route": "template.html.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\tbtfvwlp9r-7q71xfk5fx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2135"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SbhBYQfXDESsl5ePmNHcEswzYdv19UPTrQwaekWo3eA="}]}, {"Route": "template.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\hyx4dqjg3v-g0zvhtklle.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000403225806"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2479"}, {"Name": "ETag", "Value": "\"UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM="}]}, {"Route": "template.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\template.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8614"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:32:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AHjL0ouwfVLrRNYdcrfpdyj8AhFdmSKatyNPgSIvylM="}]}, {"Route": "template.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\hyx4dqjg3v-g0zvhtklle.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2479"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 10:35:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UnzTUeuYXVVrq02PMUyPTrBciFTV8/uhHGUwwLLCNmU="}]}]}