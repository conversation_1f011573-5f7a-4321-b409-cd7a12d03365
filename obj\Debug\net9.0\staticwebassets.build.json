{"Version": 1, "Hash": "W11H/W0oX04ok+bmPwCyNYMCv9jMf7lhmkmO353giwY=", "Source": "WordExtractor", "BasePath": "_content/WordExtractor", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "WordExtractor\\wwwroot", "Source": "WordExtractor", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-h77qf1ra26.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint=h77qf1ra26}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "tpbcgmyjx9", "Integrity": "rzGPGzISRhXBSCog8sB0nI7ONV44ZB0eH1VCXxV9n5k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "FileLength": 1913, "LastWriteTime": "2025-06-09T09:06:45+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint=v20ta0p5ky}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "z259h50fpz", "Integrity": "+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "FileLength": 4971, "LastWriteTime": "2025-06-09T09:06:45+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v20ta0p5ky", "Integrity": "WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 21340, "LastWriteTime": "2025-06-09T09:06:09+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h77qf1ra26", "Integrity": "AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 8359, "LastWriteTime": "2025-06-09T09:03:35+00:00"}], "Endpoints": [{"Route": "app.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000201126307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk="}]}, {"Route": "app.v20ta0p5ky.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000201126307"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.v20ta0p5ky.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-WUBP+lRlbojIPeL1d0J68F13GnmdJCxNfH4k21MbJBQ="}]}, {"Route": "app.v20ta0p5ky.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-v20ta0p5ky.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4971"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v20ta0p5ky"}, {"Name": "label", "Value": "app.js.gz"}, {"Name": "integrity", "Value": "sha256-+ya62mVCD2p9lSiH364+/VTGj0LUx+IVLv9lBTGRXlk="}]}, {"Route": "index.h77qf1ra26.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-h77qf1ra26.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000522466040"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1913"}, {"Name": "ETag", "Value": "\"rzGPGzISRhXBSCog8sB0nI7ONV44ZB0eH1VCXxV9n5k=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h77qf1ra26"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4="}]}, {"Route": "index.h77qf1ra26.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8359"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:03:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h77qf1ra26"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4="}]}, {"Route": "index.h77qf1ra26.html.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-h77qf1ra26.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1913"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"rzGPGzISRhXBSCog8sB0nI7ONV44ZB0eH1VCXxV9n5k=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h77qf1ra26"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-rzGPGzISRhXBSCog8sB0nI7ONV44ZB0eH1VCXxV9n5k="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-h77qf1ra26.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000522466040"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1913"}, {"Name": "ETag", "Value": "\"rzGPGzISRhXBSCog8sB0nI7ONV44ZB0eH1VCXxV9n5k=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8359"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:03:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-AxYDhbbrEjTgFyoUvRUv3dtSnqjnvr6IzIv5d5qOXx4="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-h77qf1ra26.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1913"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"rzGPGzISRhXBSCog8sB0nI7ONV44ZB0eH1VCXxV9n5k=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 09:06:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rzGPGzISRhXBSCog8sB0nI7ONV44ZB0eH1VCXxV9n5k="}]}]}