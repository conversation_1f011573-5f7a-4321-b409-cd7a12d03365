{"Version": 1, "Hash": "VM2UH/L7tp9UIWAEiU9edGVQDWS5yo//e+G7+Z1QyvY=", "Source": "WordExtractor", "BasePath": "_content/WordExtractor", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "WordExtractor\\wwwroot", "Source": "WordExtractor", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-gyy8itz0sx.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint=gyy8itz0sx}]?.html.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "at5m72mjla", "Integrity": "7pX/I+NEQPlp3DEbP3Yhg9bxiDMpIP5kcHD/BJtZ08A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "FileLength": 1566, "LastWriteTime": "2025-06-09T08:36:25+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-k6oxa3pozb.gz", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint=k6oxa3pozb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hdm46t5nwi", "Integrity": "RbSM22zIc81lwTzmZ2yOZHXRyayNh0PGIoxH0sFeFWk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "FileLength": 3253, "LastWriteTime": "2025-06-09T08:36:25+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "app#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k6oxa3pozb", "Integrity": "VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.js", "FileLength": 12293, "LastWriteTime": "2025-06-09T08:23:08+00:00"}, {"Identity": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "SourceId": "WordExtractor", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\", "BasePath": "_content/WordExtractor", "RelativePath": "index#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gyy8itz0sx", "Integrity": "+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\index.html", "FileLength": 6035, "LastWriteTime": "2025-06-09T08:21:55+00:00"}], "Endpoints": [{"Route": "app.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-k6oxa3pozb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307314075"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3253"}, {"Name": "ETag", "Value": "\"RbSM22zIc81lwTzmZ2yOZHXRyayNh0PGIoxH0sFeFWk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:36:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA="}]}, {"Route": "app.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:23:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA="}]}, {"Route": "app.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-k6oxa3pozb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3253"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RbSM22zIc81lwTzmZ2yOZHXRyayNh0PGIoxH0sFeFWk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:36:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RbSM22zIc81lwTzmZ2yOZHXRyayNh0PGIoxH0sFeFWk="}]}, {"Route": "app.k6oxa3pozb.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-k6oxa3pozb.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000307314075"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3253"}, {"Name": "ETag", "Value": "\"RbSM22zIc81lwTzmZ2yOZHXRyayNh0PGIoxH0sFeFWk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:36:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "W/\"VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k6oxa3pozb"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA="}]}, {"Route": "app.k6oxa3pozb.js", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\app.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:23:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k6oxa3pozb"}, {"Name": "label", "Value": "app.js"}, {"Name": "integrity", "Value": "sha256-VEH699PZWPC1voQVMJF3PHtTtmoCy1ju56CDRTu+DnA="}]}, {"Route": "app.k6oxa3pozb.js.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\2ffr2s4p3c-k6oxa3pozb.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3253"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"RbSM22zIc81lwTzmZ2yOZHXRyayNh0PGIoxH0sFeFWk=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:36:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k6oxa3pozb"}, {"Name": "label", "Value": "app.js.gz"}, {"Name": "integrity", "Value": "sha256-RbSM22zIc81lwTzmZ2yOZHXRyayNh0PGIoxH0sFeFWk="}]}, {"Route": "index.gyy8itz0sx.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-gyy8itz0sx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000638162093"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1566"}, {"Name": "ETag", "Value": "\"7pX/I+NEQPlp3DEbP3Yhg9bxiDMpIP5kcHD/BJtZ08A=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:36:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gyy8itz0sx"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g="}]}, {"Route": "index.gyy8itz0sx.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6035"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:21:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gyy8itz0sx"}, {"Name": "label", "Value": "index.html"}, {"Name": "integrity", "Value": "sha256-+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g="}]}, {"Route": "index.gyy8itz0sx.html.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-gyy8itz0sx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1566"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7pX/I+NEQPlp3DEbP3Yhg9bxiDMpIP5kcHD/BJtZ08A=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:36:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gyy8itz0sx"}, {"Name": "label", "Value": "index.html.gz"}, {"Name": "integrity", "Value": "sha256-7pX/I+NEQPlp3DEbP3Yhg9bxiDMpIP5kcHD/BJtZ08A="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-gyy8itz0sx.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000638162093"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1566"}, {"Name": "ETag", "Value": "\"7pX/I+NEQPlp3DEbP3Yhg9bxiDMpIP5kcHD/BJtZ08A=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:36:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "W/\"+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g="}]}, {"Route": "index.html", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\wwwroot\\index.html", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6035"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:21:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+Hg3lG5xaPNCcHOb3I4o2fKnQFc7ahBtu4tboDibm7g="}]}, {"Route": "index.html.gz", "AssetFile": "C:\\Users\\<USER>\\Documents\\augment-projects\\word\\obj\\Debug\\net9.0\\compressed\\0dncnyz89e-gyy8itz0sx.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1566"}, {"Name": "Content-Type", "Value": "text/html"}, {"Name": "ETag", "Value": "\"7pX/I+NEQPlp3DEbP3Yhg9bxiDMpIP5kcHD/BJtZ08A=\""}, {"Name": "Last-Modified", "Value": "Mon, 09 Jun 2025 08:36:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7pX/I+NEQPlp3DEbP3Yhg9bxiDMpIP5kcHD/BJtZ08A="}]}]}