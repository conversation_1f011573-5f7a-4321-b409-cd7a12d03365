#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档标题分析工具
分析Word文档中的所有标题，包括内置目录和样式标题
"""

import os
import sys
from docx import Document
from docx.shared import Inches
import json

def analyze_word_document(file_path):
    """
    分析Word文档，提取所有标题
    
    Args:
        file_path (str): Word文档路径
        
    Returns:
        dict: 包含标题信息的字典
    """
    try:
        # 打开Word文档
        doc = Document(file_path)
        
        # 存储结果
        result = {
            'file_name': os.path.basename(file_path),
            'headings': [],
            'toc_entries': [],
            'paragraph_styles': [],
            'total_paragraphs': len(doc.paragraphs)
        }
        
        print(f"正在分析文档: {file_path}")
        print(f"总段落数: {len(doc.paragraphs)}")
        print("-" * 50)
        
        # 分析每个段落
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if not text:
                continue
                
            # 获取段落样式
            style_name = paragraph.style.name if paragraph.style else "Normal"
            
            # 检查是否为标题样式
            is_heading = False
            heading_level = 0
            
            if style_name.startswith('Heading') or style_name.startswith('标题'):
                is_heading = True
                # 尝试提取标题级别
                try:
                    if 'Heading' in style_name:
                        heading_level = int(style_name.split()[-1])
                    elif '标题' in style_name:
                        # 处理中文标题样式
                        level_part = style_name.replace('标题', '').strip()
                        if level_part.isdigit():
                            heading_level = int(level_part)
                        elif level_part == '':
                            heading_level = 1
                except:
                    heading_level = 1
            
            # 检查是否包含目录相关关键词
            is_toc_related = any(keyword in text.lower() for keyword in 
                               ['目录', 'contents', 'table of contents', '目　录'])
            
            # 记录段落信息
            paragraph_info = {
                'index': i,
                'text': text,
                'style': style_name,
                'is_heading': is_heading,
                'heading_level': heading_level,
                'is_toc_related': is_toc_related,
                'char_count': len(text)
            }
            
            # 如果是标题，添加到标题列表
            if is_heading:
                result['headings'].append(paragraph_info)
                print(f"标题 {heading_level}: {text}")
            
            # 如果是目录相关，添加到目录列表
            if is_toc_related:
                result['toc_entries'].append(paragraph_info)
                print(f"目录项: {text}")
            
            # 记录所有段落样式
            if style_name not in [item['style'] for item in result['paragraph_styles']]:
                result['paragraph_styles'].append({
                    'style': style_name,
                    'example_text': text[:50] + ('...' if len(text) > 50 else '')
                })
        
        # 分析表格中的内容（可能包含目录）
        print("\n" + "="*50)
        print("分析表格内容...")
        
        table_headings = []
        for table_idx, table in enumerate(doc.tables):
            print(f"\n表格 {table_idx + 1}:")
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    cell_text = cell.text.strip()
                    if cell_text:
                        # 检查是否可能是目录项
                        if any(char.isdigit() for char in cell_text) and len(cell_text) > 3:
                            table_entry = {
                                'table_index': table_idx,
                                'row': row_idx,
                                'cell': cell_idx,
                                'text': cell_text,
                                'is_potential_toc': True
                            }
                            table_headings.append(table_entry)
                            print(f"  可能的目录项: {cell_text}")
        
        result['table_entries'] = table_headings
        
        return result
        
    except Exception as e:
        print(f"分析文档时出错: {str(e)}")
        return None

def print_summary(result):
    """打印分析结果摘要"""
    if not result:
        return
        
    print("\n" + "="*60)
    print("分析结果摘要")
    print("="*60)
    
    print(f"文档名称: {result['file_name']}")
    print(f"总段落数: {result['total_paragraphs']}")
    print(f"发现标题数: {len(result['headings'])}")
    print(f"目录相关项: {len(result['toc_entries'])}")
    print(f"表格目录项: {len(result['table_entries'])}")
    
    if result['headings']:
        print("\n标题列表:")
        for heading in result['headings']:
            indent = "  " * (heading['heading_level'] - 1)
            print(f"{indent}• {heading['text']} (级别 {heading['heading_level']})")
    
    if result['toc_entries']:
        print("\n目录相关内容:")
        for toc in result['toc_entries']:
            print(f"• {toc['text']}")
    
    if result['table_entries']:
        print("\n表格中的潜在目录项:")
        for entry in result['table_entries']:
            print(f"• {entry['text']}")
    
    print("\n文档中使用的样式:")
    for style_info in result['paragraph_styles']:
        print(f"• {style_info['style']}: {style_info['example_text']}")

def main():
    """主函数"""
    # 查找当前目录下的Word文档
    word_files = []
    for file in os.listdir('.'):
        if file.endswith(('.docx', '.doc')):
            word_files.append(file)
    
    if not word_files:
        print("当前目录下没有找到Word文档")
        return
    
    print(f"找到 {len(word_files)} 个Word文档:")
    for file in word_files:
        print(f"• {file}")
    
    # 分析每个Word文档
    all_results = []
    for word_file in word_files:
        print(f"\n{'='*60}")
        print(f"开始分析: {word_file}")
        print('='*60)
        
        result = analyze_word_document(word_file)
        if result:
            all_results.append(result)
            print_summary(result)
            
            # 保存详细结果到JSON文件
            json_file = f"{os.path.splitext(word_file)[0]}_analysis.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"\n详细分析结果已保存到: {json_file}")
    
    print(f"\n{'='*60}")
    print("分析完成!")
    print('='*60)

if __name__ == "__main__":
    main()
