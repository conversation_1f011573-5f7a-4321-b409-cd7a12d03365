#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取Word文档中特定标题的内容并创建新文档
保留原格式
"""

import os
import sys
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import json

def find_section_content(doc, target_title):
    """
    查找特定标题的内容

    Args:
        doc: Word文档对象
        target_title: 目标标题

    Returns:
        tuple: (开始段落索引, 结束段落索引, 相关表格索引列表)
    """
    start_index = None
    end_index = None
    table_indices = []

    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()

        # 查找目标标题
        if target_title in text:
            start_index = i
            print(f"找到目标标题: {text} (段落 {i})")
            continue

        # 如果已经找到开始位置，寻找下一个主要标题作为结束位置
        if start_index is not None and end_index is None:
            # 检查是否为下一个主要标题
            style_name = paragraph.style.name if paragraph.style else "Normal"

            # 如果遇到"目录"或其他明确的分界标题，则结束
            if (text and
                (text == "目录" or
                 text.startswith("目录") or
                 text == "声明" or
                 any(keyword in text for keyword in ['测评项目概述', '被测对象描述', '单项测评', '整体测评', '1 测评项目概述']))):
                end_index = i
                print(f"找到结束位置: {text[:30]}... (段落 {i})")
                break

    # 如果没有找到明确的结束位置，查找接下来的50个段落
    if start_index is not None and end_index is None:
        end_index = min(start_index + 50, len(doc.paragraphs))
        print(f"使用限制范围作为结束位置 (段落 {end_index})")

    # 查找相关的表格（在标题附近的表格）
    if start_index is not None:
        # 检查文档中的表格，找到在目标范围内的表格
        for table_idx, table in enumerate(doc.tables):
            # 简化：只取前几个表格，这些通常是基本信息表
            if table_idx < 3:  # 只取前3个表格
                table_indices.append(table_idx)
                print(f"包含表格 {table_idx}")

    return start_index, end_index, table_indices

def copy_section_to_new_doc(source_doc, start_index, end_index, table_indices, target_title):
    """
    将指定段落范围复制到新文档

    Args:
        source_doc: 源文档
        start_index: 开始段落索引
        end_index: 结束段落索引
        table_indices: 要复制的表格索引列表
        target_title: 目标标题

    Returns:
        Document: 新文档对象
    """
    # 创建新文档
    new_doc = Document()

    print(f"正在复制段落 {start_index} 到 {end_index-1}...")

    # 复制段落
    for i in range(start_index, end_index):
        if i >= len(source_doc.paragraphs):
            break

        source_para = source_doc.paragraphs[i]
        text = source_para.text.strip()

        # 跳过空段落
        if not text:
            continue

        # 创建新段落
        new_para = new_doc.add_paragraph()

        # 复制段落样式
        try:
            new_para.style = source_para.style
        except:
            # 如果样式不存在，使用默认样式
            pass

        # 复制段落对齐方式
        new_para.alignment = source_para.alignment

        # 复制文本内容和格式
        for run in source_para.runs:
            new_run = new_para.add_run(run.text)

            # 复制字体格式
            if run.font.name:
                new_run.font.name = run.font.name
            if run.font.size:
                new_run.font.size = run.font.size
            if run.font.bold:
                new_run.font.bold = run.font.bold
            if run.font.italic:
                new_run.font.italic = run.font.italic
            if run.font.underline:
                new_run.font.underline = run.font.underline

    # 复制指定的表格
    print("正在复制相关表格...")
    table_count = 0
    for table_idx in table_indices:
        if table_idx < len(source_doc.tables):
            try:
                table = source_doc.tables[table_idx]
                new_table = new_doc.add_table(rows=len(table.rows), cols=len(table.columns))

                # 复制表格内容
                for row_idx, row in enumerate(table.rows):
                    for col_idx, cell in enumerate(row.cells):
                        new_table.cell(row_idx, col_idx).text = cell.text

                        # 尝试复制单元格格式
                        try:
                            new_cell = new_table.cell(row_idx, col_idx)
                            for para in cell.paragraphs:
                                if para.text.strip():
                                    new_cell_para = new_cell.paragraphs[0] if new_cell.paragraphs else new_cell.add_paragraph()
                                    new_cell_para.text = para.text
                                    new_cell_para.alignment = para.alignment
                        except:
                            pass

                table_count += 1
                print(f"已复制表格 {table_idx + 1}")

            except Exception as e:
                print(f"复制表格 {table_idx} 时出错: {str(e)}")

    return new_doc

def extract_specific_section(file_path, target_title):
    """
    提取特定标题的内容
    
    Args:
        file_path: Word文档路径
        target_title: 目标标题
        
    Returns:
        bool: 是否成功
    """
    try:
        # 打开源文档
        doc = Document(file_path)
        
        print(f"正在分析文档: {file_path}")
        print(f"查找标题: {target_title}")
        print("-" * 50)
        
        # 查找目标标题的内容范围
        start_index, end_index, table_indices = find_section_content(doc, target_title)

        if start_index is None:
            print(f"未找到标题: {target_title}")
            return False

        print(f"找到内容范围: 段落 {start_index} 到 {end_index-1}")
        print(f"相关表格: {table_indices}")

        # 复制内容到新文档
        new_doc = copy_section_to_new_doc(doc, start_index, end_index, table_indices, target_title)
        
        # 保存新文档
        safe_title = target_title.replace('/', '_').replace('\\', '_').replace(':', '_')
        output_file = f"{safe_title}.docx"
        new_doc.save(output_file)
        
        print(f"\n✅ 成功提取内容到: {output_file}")
        print(f"📄 提取了 {end_index - start_index} 个段落")
        
        return True
        
    except Exception as e:
        print(f"❌ 提取内容时出错: {str(e)}")
        return False

def main():
    """主函数"""
    # 查找Word文档
    word_files = [f for f in os.listdir('.') if f.endswith(('.docx', '.doc'))]
    
    if not word_files:
        print("❌ 当前目录下没有找到Word文档")
        return
    
    # 使用第一个找到的Word文档
    source_file = word_files[0]
    target_title = "网络安全等级测评基本信息表"
    
    print(f"📖 源文档: {source_file}")
    print(f"🎯 目标标题: {target_title}")
    print("=" * 60)
    
    # 提取指定标题的内容
    success = extract_specific_section(source_file, target_title)
    
    if success:
        print("\n🎉 提取完成!")
    else:
        print("\n❌ 提取失败!")

if __name__ == "__main__":
    main()
