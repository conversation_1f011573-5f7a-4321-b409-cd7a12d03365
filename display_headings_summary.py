#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示Word文档标题分析结果摘要
"""

import json
import os

def display_headings_tree(headings):
    """以树状结构显示标题"""
    print("\n📋 文档标题结构:")
    print("=" * 60)
    
    for i, heading in enumerate(headings, 1):
        level = heading['heading_level']
        text = heading['text']
        
        # 根据级别添加缩进
        indent = "  " * (level - 1)
        
        # 根据级别选择不同的符号
        if level == 1:
            symbol = "📁"
        elif level == 2:
            symbol = "📂"
        elif level == 3:
            symbol = "📄"
        else:
            symbol = "📝"
        
        print(f"{indent}{symbol} {text}")

def display_statistics(result):
    """显示统计信息"""
    print("\n📊 文档统计信息:")
    print("=" * 60)
    print(f"📄 文档名称: {result['file_name']}")
    print(f"📝 总段落数: {result['total_paragraphs']}")
    print(f"🏷️  发现标题数: {len(result['headings'])}")
    print(f"📑 目录相关项: {len(result['toc_entries'])}")
    print(f"📊 表格目录项: {len(result['table_entries'])}")
    
    # 按级别统计标题
    level_counts = {}
    for heading in result['headings']:
        level = heading['heading_level']
        level_counts[level] = level_counts.get(level, 0) + 1
    
    print(f"\n📋 标题级别分布:")
    for level in sorted(level_counts.keys()):
        print(f"  级别 {level}: {level_counts[level]} 个")

def display_toc_info(result):
    """显示目录信息"""
    if result['toc_entries']:
        print(f"\n📑 目录相关内容:")
        print("=" * 60)
        for toc in result['toc_entries']:
            print(f"• {toc['text']}")

def main():
    """主函数"""
    # 查找分析结果文件
    json_files = [f for f in os.listdir('.') if f.endswith('_analysis.json')]
    
    if not json_files:
        print("❌ 未找到分析结果文件")
        return
    
    for json_file in json_files:
        print(f"\n{'='*80}")
        print(f"📖 分析结果: {json_file}")
        print('='*80)
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                result = json.load(f)
            
            # 显示统计信息
            display_statistics(result)
            
            # 显示标题树状结构
            if result['headings']:
                display_headings_tree(result['headings'])
            
            # 显示目录信息
            display_toc_info(result)
            
            print(f"\n✅ 分析完成! 详细信息请查看: {json_file}")
            
        except Exception as e:
            print(f"❌ 读取文件 {json_file} 时出错: {str(e)}")

if __name__ == "__main__":
    main()
