@echo off
echo 正在启动Word文档提取工具...
echo.

REM 检查是否安装了.NET 8
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到.NET 8 SDK
    echo 请从以下地址下载并安装.NET 8 SDK:
    echo https://dotnet.microsoft.com/download/dotnet/8.0
    pause
    exit /b 1
)

echo 检测到.NET版本:
dotnet --version

echo.
echo 正在还原NuGet包...
dotnet restore

if %errorlevel% neq 0 (
    echo 错误: NuGet包还原失败
    pause
    exit /b 1
)

echo.
echo 正在构建项目...
dotnet build

if %errorlevel% neq 0 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

echo.
echo 正在启动Web服务器...
echo 应用程序将在以下地址运行:
echo   - HTTP:  http://localhost:5000
echo   - HTTPS: https://localhost:5001
echo.
echo 按 Ctrl+C 停止服务器
echo.

dotnet run

pause
