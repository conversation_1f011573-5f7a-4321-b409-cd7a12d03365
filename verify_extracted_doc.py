#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证提取的Word文档内容
"""

import os
from docx import Document

def verify_document(file_path):
    """
    验证文档内容
    
    Args:
        file_path: 文档路径
    """
    try:
        doc = Document(file_path)
        
        print(f"📖 验证文档: {file_path}")
        print("=" * 60)
        
        # 统计信息
        print(f"📄 段落数: {len(doc.paragraphs)}")
        print(f"📊 表格数: {len(doc.tables)}")
        
        # 显示段落内容
        print(f"\n📝 段落内容:")
        print("-" * 40)
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if text:
                print(f"{i+1:2d}. {text}")
        
        # 显示表格信息
        if doc.tables:
            print(f"\n📊 表格信息:")
            print("-" * 40)
            for i, table in enumerate(doc.tables):
                print(f"表格 {i+1}: {len(table.rows)} 行 x {len(table.columns)} 列")
                
                # 显示表格的前几行内容
                for row_idx, row in enumerate(table.rows[:3]):  # 只显示前3行
                    row_text = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        if cell_text:
                            row_text.append(cell_text[:20] + ('...' if len(cell_text) > 20 else ''))
                    if row_text:
                        print(f"  行 {row_idx+1}: {' | '.join(row_text)}")
                
                if len(table.rows) > 3:
                    print(f"  ... (还有 {len(table.rows) - 3} 行)")
                print()
        
        print("✅ 文档验证完成!")
        
    except Exception as e:
        print(f"❌ 验证文档时出错: {str(e)}")

def main():
    """主函数"""
    target_file = "网络安全等级测评基本信息表.docx"
    
    if os.path.exists(target_file):
        verify_document(target_file)
    else:
        print(f"❌ 文件不存在: {target_file}")

if __name__ == "__main__":
    main()
