#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试"网络安全等级测评基本信息表"的提取
"""

import requests
import os
from docx import Document

def test_basic_info_table():
    """
    测试基本信息表的提取
    """
    print("🎯 测试: 网络安全等级测评基本信息表")
    print("="*50)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return False
    
    try:
        # 调用API提取
        base_url = "http://localhost:5000/api/wordextraction"
        title = "网络安全等级测评基本信息表"
        
        with open(source_file, 'rb') as f:
            files = {'file': f}
            data = {
                'TargetTitle': title,
                'PreserveFormatting': 'true',
                'IncludeTables': 'true'
            }
            response = requests.post(f"{base_url}/extract", files=files, data=data)
        
        if response.status_code == 200:
            # 保存提取的文档
            output_file = f"测试_基本信息表.docx"
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            # 分析提取的内容
            doc = Document(output_file)
            
            print(f"✅ 提取成功: {output_file}")
            print(f"📄 段落数: {len(doc.paragraphs)}")
            print(f"📊 表格数: {len(doc.tables)}")
            print(f"📁 文件大小: {os.path.getsize(output_file):,} 字节")
            
            # 显示段落内容
            print(f"\n📝 段落内容:")
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if text:
                    print(f"  {i+1}. {text}")
            
            # 显示表格内容
            if doc.tables:
                print(f"\n📊 表格内容:")
                for i, table in enumerate(doc.tables):
                    print(f"  表格 {i+1}: {len(table.rows)} 行 x {len(table.columns)} 列")
                    
                    # 显示表格前几行
                    for row_idx, row in enumerate(table.rows[:3]):
                        row_text = []
                        for cell in row.cells:
                            cell_text = cell.text.strip()
                            if cell_text:
                                row_text.append(cell_text[:25] + ('...' if len(cell_text) > 25 else ''))
                        if row_text:
                            print(f"    行 {row_idx+1}: {' | '.join(row_text)}")
                    
                    if len(table.rows) > 3:
                        print(f"    ... (还有 {len(table.rows) - 3} 行)")
            else:
                print(f"\n⚠️ 没有找到表格！这可能是个问题。")
            
            return True
            
        else:
            print(f"❌ 提取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    test_basic_info_table()
