#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证C# Web版本提取的准确性
确认提取的内容是否与标题真正对应
"""

import requests
import os
from docx import Document

def validate_extraction(title, expected_content_type):
    """
    验证特定标题的提取准确性
    
    Args:
        title: 要提取的标题
        expected_content_type: 期望的内容类型描述
    """
    print(f"🎯 验证标题: {title}")
    print(f"📋 期望内容: {expected_content_type}")
    print("-" * 50)
    
    # 检查源文档
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return False
    
    try:
        # 调用API提取
        base_url = "http://localhost:5000/api/wordextraction"
        with open(source_file, 'rb') as f:
            files = {'file': f}
            data = {
                'TargetTitle': title,
                'PreserveFormatting': 'true',
                'IncludeTables': 'true'
            }
            response = requests.post(f"{base_url}/extract", files=files, data=data)
        
        if response.status_code == 200:
            # 保存提取的文档
            output_file = f"验证_{title.replace('/', '_').replace('\\', '_').replace(':', '_')}.docx"
            with open(output_file, 'wb') as f:
                f.write(response.content)
            
            # 分析提取的内容
            doc = Document(output_file)
            
            print(f"✅ 提取成功: {output_file}")
            print(f"📄 段落数: {len(doc.paragraphs)}")
            print(f"📊 表格数: {len(doc.tables)}")
            print(f"📁 文件大小: {os.path.getsize(output_file):,} 字节")
            
            # 显示内容摘要
            print(f"\n📝 内容摘要:")
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if text:
                    print(f"  段落 {i+1}: {text}")
            
            if doc.tables:
                print(f"\n📊 表格摘要:")
                for i, table in enumerate(doc.tables):
                    print(f"  表格 {i+1}: {len(table.rows)} 行 x {len(table.columns)} 列")
                    
                    # 显示表格第一行作为标识
                    if table.rows:
                        first_row = table.rows[0]
                        row_text = []
                        for cell in first_row.cells:
                            cell_text = cell.text.strip()
                            if cell_text:
                                row_text.append(cell_text[:20] + ('...' if len(cell_text) > 20 else ''))
                        if row_text:
                            print(f"    首行: {' | '.join(row_text)}")
            
            return True
            
        else:
            print(f"❌ 提取失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务器")
        print("💡 请确保Web应用程序正在运行")
        return False
    except Exception as e:
        print(f"❌ 验证过程中出错: {str(e)}")
        return False

def main():
    """
    主验证函数
    """
    print("🔍 C# Web版本提取准确性验证")
    print("="*60)
    
    # 定义要验证的标题和期望内容
    test_cases = [
        {
            "title": "网络安全等级测评基本信息表",
            "expected": "应该只包含一个基本信息表格，包含被测对象信息"
        },
        {
            "title": "测评项目概述", 
            "expected": "应该包含测评目的、依据、过程等相关段落"
        },
        {
            "title": "被测对象描述",
            "expected": "应该包含被测对象的详细描述信息"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*60}")
        print(f"测试 {i}/{len(test_cases)}")
        print('='*60)
        
        success = validate_extraction(test_case["title"], test_case["expected"])
        if success:
            success_count += 1
        
        print()
    
    print(f"{'='*60}")
    print(f"验证完成: {success_count}/{len(test_cases)} 个测试通过")
    print('='*60)
    
    if success_count == len(test_cases):
        print("🎉 所有测试通过！C# Web版本提取准确性已验证。")
        print("💡 提取的内容与标题精确对应，没有包含无关内容。")
    else:
        print("⚠️ 部分测试失败，需要进一步调整。")

if __name__ == "__main__":
    main()
