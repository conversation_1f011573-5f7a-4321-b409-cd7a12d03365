#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试没有序号的标题提取
"""

import requests
import os
from docx import Document

def test_no_number_titles():
    """
    测试没有序号的标题
    """
    print("🔍 测试没有序号的标题提取")
    print("="*50)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return
    
    # 首先分析文档，找出没有序号的标题
    print("📋 1. 分析文档，查找没有序号的标题")
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        with open(source_file, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/analyze", files=files)
        
        if response.status_code == 200:
            analysis = response.json()
            
            print(f"✅ 文档分析成功")
            print(f"📊 目录项数: {len(analysis['tocEntries'])}")
            
            # 查找没有序号的标题
            no_number_titles = []
            for entry in analysis['tocEntries']:
                title = entry['title']
                # 检查是否没有数字开头
                import re
                if not re.match(r'^\d+', title.strip()):
                    no_number_titles.append(title)
            
            print(f"\n🔍 找到 {len(no_number_titles)} 个没有序号的标题:")
            for i, title in enumerate(no_number_titles[:10]):  # 只显示前10个
                print(f"   {i+1}. {title}")
            
            if len(no_number_titles) > 10:
                print(f"   ... 还有 {len(no_number_titles) - 10} 个")
            
            return no_number_titles[:5]  # 返回前5个用于测试
            
        else:
            print(f"❌ 文档分析失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ 分析过程中出错: {str(e)}")
        return []

def test_extract_no_number_titles(titles):
    """
    测试提取没有序号的标题
    """
    print(f"\n📤 2. 测试提取没有序号的标题")
    print("-" * 40)
    
    if not titles:
        print("❌ 没有找到无序号标题进行测试")
        return
    
    base_url = "http://localhost:5000/api/wordextraction"
    source_file = "测评报告.docx"
    
    success_count = 0
    
    for i, title in enumerate(titles, 1):
        print(f"\n   测试 {i}: {title}")
        
        try:
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': title,
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存文件
                safe_title = title.replace('/', '_').replace('\\', '_').replace(':', '_').replace('.', '_').replace(' ', '_')
                output_file = f"无序号测试_{safe_title}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                # 分析文件
                doc = Document(output_file)
                file_size = os.path.getsize(output_file)
                
                print(f"     ✅ 提取成功: {output_file}")
                print(f"     📄 段落数: {len(doc.paragraphs)}")
                print(f"     📊 表格数: {len(doc.tables)}")
                print(f"     📁 文件大小: {file_size:,} 字节")
                
                # 检查内容质量
                all_text = " ".join([p.text.strip() for p in doc.paragraphs if p.text.strip()])
                print(f"     📝 内容长度: {len(all_text)} 字符")
                
                if len(all_text) > 50:
                    print(f"     📖 内容预览: {all_text[:100]}...")
                    success_count += 1
                else:
                    print(f"     ⚠️ 内容太少，可能提取不完整")
                
            else:
                print(f"     ❌ 提取失败: {response.status_code}")
                print(f"     错误信息: {response.text}")
                
        except Exception as e:
            print(f"     ❌ 提取出错: {str(e)}")
    
    print(f"\n📊 无序号标题提取结果: {success_count}/{len(titles)} 成功")
    return success_count == len(titles)

def find_problematic_titles():
    """
    查找可能有问题的标题
    """
    print(f"\n🔍 3. 查找可能有问题的标题")
    print("-" * 40)
    
    # 一些常见的无序号标题
    test_titles = [
        "声明",
        "等级测评结论", 
        "重大风险隐患及整改建议",
        "测评项目概述",
        "被测对象描述",
        "目录"
    ]
    
    base_url = "http://localhost:5000/api/wordextraction"
    source_file = "测评报告.docx"
    
    for title in test_titles:
        print(f"\n   测试标题: {title}")
        
        try:
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': title,
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                print(f"     ✅ 提取成功")
            else:
                print(f"     ❌ 提取失败: {response.status_code}")
                print(f"     错误信息: {response.text}")
                
        except Exception as e:
            print(f"     ❌ 提取出错: {str(e)}")

def main():
    """
    主测试函数
    """
    print("🔧 测试没有序号的标题提取功能")
    print("="*60)
    
    # 分析文档，找出无序号标题
    no_number_titles = test_no_number_titles()
    
    # 测试提取无序号标题
    if no_number_titles:
        test_extract_no_number_titles(no_number_titles)
    
    # 测试一些常见的问题标题
    find_problematic_titles()
    
    print(f"\n💡 如果发现问题，需要优化标题匹配逻辑以支持无序号标题")

if __name__ == "__main__":
    main()
