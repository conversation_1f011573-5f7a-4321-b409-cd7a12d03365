#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的调试测试
"""

import requests
import os

def simple_test():
    """
    简单测试一个标题
    """
    print("🔍 简单调试测试")
    print("="*30)
    
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return
    
    # 测试标题
    test_title = "1.1 测评目的"
    
    try:
        base_url = "http://localhost:5000/api/wordextraction"
        
        with open(source_file, 'rb') as f:
            files = {'file': f}
            data = {
                'TargetTitle': test_title,
                'PreserveFormatting': 'true',
                'IncludeTables': 'true'
            }
            print(f"📤 发送提取请求: {test_title}")
            response = requests.post(f"{base_url}/extract", files=files, data=data)
        
        print(f"📥 响应状态: {response.status_code}")
        if response.status_code != 200:
            print(f"❌ 错误信息: {response.text}")
        else:
            print(f"✅ 提取成功")
            
    except Exception as e:
        print(f"❌ 测试出错: {str(e)}")

if __name__ == "__main__":
    simple_test()
