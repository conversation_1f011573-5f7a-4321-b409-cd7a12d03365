{"file_name": "测评报告.docx", "headings": [{"index": 198, "text": "测评项目概述", "style": "Heading 1", "is_heading": true, "heading_level": 1, "is_toc_related": false, "char_count": 6}, {"index": 199, "text": "测评目的", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 4}, {"index": 202, "text": "测评依据", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 4}, {"index": 211, "text": "测评过程", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 4}, {"index": 219, "text": "报告分发范围", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 221, "text": "被测对象描述", "style": "Heading 1", "is_heading": true, "heading_level": 1, "is_toc_related": false, "char_count": 6}, {"index": 222, "text": "被测对象概述", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 223, "text": "定级结果", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 4}, {"index": 225, "text": "业务和采用的技术", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 8}, {"index": 230, "text": "网络结构", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 4}, {"index": 234, "text": "测评指标", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 4}, {"index": 235, "text": "安全通用要求指标", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 8}, {"index": 237, "text": "安全扩展要求指标", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 8}, {"index": 239, "text": "其他安全要求指标", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 8}, {"index": 241, "text": "不适用安全要求指标", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 9}, {"index": 243, "text": "测评对象", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 4}, {"index": 244, "text": "测评对象选择方法", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 8}, {"index": 260, "text": "测评对象选择结果", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 8}, {"index": 261, "text": "物理机房", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 4}, {"index": 263, "text": "网络设备", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 4}, {"index": 265, "text": "安全设备", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 4}, {"index": 267, "text": "服务器", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 3}, {"index": 269, "text": "终端设备", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 4}, {"index": 271, "text": "其他系统或设备", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 7}, {"index": 273, "text": "系统管理软件/平台", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 9}, {"index": 275, "text": "业务应用系统/平台", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 9}, {"index": 277, "text": "数据资源", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 4}, {"index": 280, "text": "安全相关人员", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 6}, {"index": 282, "text": "安全管理文档", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 6}, {"index": 284, "text": "单项测评结果分析", "style": "Heading 1", "is_heading": true, "heading_level": 1, "is_toc_related": false, "char_count": 8}, {"index": 286, "text": "安全物理环境", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 287, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 289, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 291, "text": "安全通信网络", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 292, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 296, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 300, "text": "安全区域边界", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 301, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 310, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 316, "text": "安全计算环境", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 317, "text": "网络设备", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 4}, {"index": 318, "text": "已有安全控制措施汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 12}, {"index": 320, "text": "主要安全问题汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 10}, {"index": 322, "text": "安全设备", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 4}, {"index": 323, "text": "已有安全控制措施汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 12}, {"index": 325, "text": "主要安全问题汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 10}, {"index": 327, "text": "服务器和终端", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 6}, {"index": 328, "text": "已有安全控制措施汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 12}, {"index": 339, "text": "主要安全问题汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 10}, {"index": 365, "text": "系统管理软件/平台", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 9}, {"index": 366, "text": "已有安全控制措施汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 12}, {"index": 376, "text": "主要安全问题汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 10}, {"index": 394, "text": "业务应用系统/平台", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 9}, {"index": 395, "text": "已有安全控制措施汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 12}, {"index": 405, "text": "主要安全问题汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 10}, {"index": 425, "text": "数据资源", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 4}, {"index": 426, "text": "已有安全控制措施汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 12}, {"index": 431, "text": "主要安全问题汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 10}, {"index": 437, "text": "其他系统或设备", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 7}, {"index": 438, "text": "已有安全控制措施汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 12}, {"index": 440, "text": "主要安全问题汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 10}, {"index": 442, "text": "安全扩展要求", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 6}, {"index": 443, "text": "已有安全控制措施汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 12}, {"index": 447, "text": "主要安全问题汇总分析", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 10}, {"index": 449, "text": "安全管理中心", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 450, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 454, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 466, "text": "安全管理制度", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 467, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 473, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 475, "text": "安全管理机构", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 476, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 483, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 487, "text": "安全管理人员", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 488, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 494, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 496, "text": "安全建设管理", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 497, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 510, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 520, "text": "安全运维管理", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 521, "text": "已有安全控制措施汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 12}, {"index": 535, "text": "主要安全问题汇总分析", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 10}, {"index": 547, "text": "其他安全要求指标", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 8}, {"index": 549, "text": "验证测试", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 4}, {"index": 550, "text": "漏洞扫描", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 4}, {"index": 551, "text": "漏洞扫描结果统计", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 8}, {"index": 565, "text": "漏洞扫描问题描述", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 8}, {"index": 568, "text": "渗透测试", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 4}, {"index": 569, "text": "渗透测试过程说明", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 8}, {"index": 614, "text": "渗透测试问题描述", "style": "Heading 4", "is_heading": true, "heading_level": 4, "is_toc_related": false, "char_count": 8}, {"index": 616, "text": "安全问题汇总", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 6}, {"index": 617, "text": "安全问题汇总", "style": "Heading 3", "is_heading": true, "heading_level": 3, "is_toc_related": false, "char_count": 6}, {"index": 620, "text": "整体测评", "style": "Heading 1", "is_heading": true, "heading_level": 1, "is_toc_related": false, "char_count": 4}, {"index": 621, "text": "安全控制点间安全测评", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 10}, {"index": 624, "text": "区域间安全测评", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 7}, {"index": 627, "text": "整体测评结果汇总", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 8}, {"index": 629, "text": "安全问题风险分析", "style": "Heading 1", "is_heading": true, "heading_level": 1, "is_toc_related": false, "char_count": 8}, {"index": 630, "text": "低中高风险安全问题分析", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 11}, {"index": 634, "text": "重大风险隐患分析", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 8}, {"index": 636, "text": "总体评价", "style": "Heading 1", "is_heading": true, "heading_level": 1, "is_toc_related": false, "char_count": 4}, {"index": 650, "text": "等级测评结论", "style": "Heading 1", "is_heading": true, "heading_level": 1, "is_toc_related": false, "char_count": 6}, {"index": 655, "text": "安全问题整改建议", "style": "Heading 1", "is_heading": true, "heading_level": 1, "is_toc_related": false, "char_count": 8}, {"index": 656, "text": "低中高风险安全问题整改建议", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 13}, {"index": 658, "text": "重大风险隐患整改建议", "style": "Heading 2", "is_heading": true, "heading_level": 2, "is_toc_related": false, "char_count": 10}], "toc_entries": [{"index": 48, "text": "目录", "style": "Title", "is_heading": false, "heading_level": 0, "is_toc_related": true, "char_count": 2}, {"index": 54, "text": "目录\tVII", "style": "toc 1", "is_heading": false, "heading_level": 0, "is_toc_related": true, "char_count": 6}, {"index": 537, "text": "（1）未提供存档介质目录清单及定期盘点记录。", "style": "Normal", "is_heading": false, "heading_level": 0, "is_toc_related": true, "char_count": 22}], "paragraph_styles": [{"style": "Normal", "example_text": "项目编号：DBCP-2024-12-0143"}, {"style": "Title", "example_text": "网络安全等级测评基本信息表"}, {"style": "Body Text First Indent", "example_text": "经过单项测评结果判定和整体测评，未发现望海康信易普优采平台存在重大风险隐患。"}, {"style": "toc 1", "example_text": "网络安全等级测评基本信息表\tI"}, {"style": "toc 2", "example_text": "1.1 测评目的\t1"}, {"style": "toc 3", "example_text": "2.1.1 定级结果\t4"}, {"style": "Heading 1", "example_text": "测评项目概述"}, {"style": "Heading 2", "example_text": "测评目的"}, {"style": "Heading 3", "example_text": "定级结果"}, {"style": "fs-4-first-line-indent-2", "example_text": "望海康信易普优采平台是望海康信专为医疗机构推出的线上采购寻源平台，支持供应商寻源、产品寻源、参数配置..."}, {"style": "Heading 4", "example_text": "物理机房"}, {"style": "Normal (Web)", "example_text": "注：针对系统配置数据、鉴别数据随着系统资产进行测评，审计数据则在安全管理中心中进行统一测评，故配置数..."}, {"style": "Caption", "example_text": "表 7-1 等级测评结论判定依据"}, {"style": "附录一级标题", "example_text": "被测对象资产"}, {"style": "附录二级标题", "example_text": "物理机房"}, {"style": "附录三级标题", "example_text": "网络设备"}, {"style": "附录四级标题", "example_text": "安全通信网络"}, {"style": "附录五级标题", "example_text": "ha-03"}], "total_paragraphs": 840, "table_entries": [{"table_index": 0, "row": 2, "cell": 1, "text": "2025年04月01日", "is_potential_toc": true}, {"table_index": 1, "row": 1, "cell": 6, "text": "第三级（S3A3）", "is_potential_toc": true}, {"table_index": 1, "row": 1, "cell": 7, "text": "第三级（S3A3）", "is_potential_toc": true}, {"table_index": 1, "row": 2, "cell": 2, "text": "11010113074-20001", "is_potential_toc": true}, {"table_index": 1, "row": 2, "cell": 3, "text": "11010113074-20001", "is_potential_toc": true}, {"table_index": 1, "row": 2, "cell": 6, "text": "91110302750103981B", "is_potential_toc": true}, {"table_index": 1, "row": 2, "cell": 7, "text": "91110302750103981B", "is_potential_toc": true}, {"table_index": 1, "row": 5, "cell": 1, "text": "北京市东城区广渠门内大街45号雍贵中心D座9层", "is_potential_toc": true}, {"table_index": 1, "row": 5, "cell": 2, "text": "北京市东城区广渠门内大街45号雍贵中心D座9层", "is_potential_toc": true}, {"table_index": 1, "row": 5, "cell": 3, "text": "北京市东城区广渠门内大街45号雍贵中心D座9层", "is_potential_toc": true}, {"table_index": 1, "row": 5, "cell": 4, "text": "北京市东城区广渠门内大街45号雍贵中心D座9层", "is_potential_toc": true}, {"table_index": 1, "row": 5, "cell": 7, "text": "100062", "is_potential_toc": true}, {"table_index": 1, "row": 7, "cell": 5, "text": "18611388445", "is_potential_toc": true}, {"table_index": 1, "row": 7, "cell": 6, "text": "18611388445", "is_potential_toc": true}, {"table_index": 1, "row": 7, "cell": 7, "text": "18611388445", "is_potential_toc": true}, {"table_index": 1, "row": 8, "cell": 3, "text": "18611388445", "is_potential_toc": true}, {"table_index": 1, "row": 10, "cell": 7, "text": "0019", "is_potential_toc": true}, {"table_index": 1, "row": 11, "cell": 1, "text": "北京市朝阳区裕民路甲3号", "is_potential_toc": true}, {"table_index": 1, "row": 11, "cell": 2, "text": "北京市朝阳区裕民路甲3号", "is_potential_toc": true}, {"table_index": 1, "row": 11, "cell": 3, "text": "北京市朝阳区裕民路甲3号", "is_potential_toc": true}, {"table_index": 1, "row": 11, "cell": 4, "text": "北京市朝阳区裕民路甲3号", "is_potential_toc": true}, {"table_index": 1, "row": 11, "cell": 7, "text": "100029", "is_potential_toc": true}, {"table_index": 1, "row": 13, "cell": 5, "text": "010-82992178", "is_potential_toc": true}, {"table_index": 1, "row": 13, "cell": 6, "text": "010-82992178", "is_potential_toc": true}, {"table_index": 1, "row": 13, "cell": 7, "text": "010-82992178", "is_potential_toc": true}, {"table_index": 1, "row": 14, "cell": 3, "text": "13811837819", "is_potential_toc": true}, {"table_index": 2, "row": 1, "cell": 5, "text": "第三级（S3A3）", "is_potential_toc": true}, {"table_index": 2, "row": 3, "cell": 1, "text": "望海康信易普优采平台于 2019 年4 月正式上线，隶属于望海康信（北京）科技股份公司。望海康信易普优采平台业务主要包含：阳光招采、市场调研、采购决策等业务，其中：阳光招采模块是医院发布采购信息，采购信息高效同步到平台，供应商根据采购信息影响报价；市场调研模块是医院发布采购调研公告到平台，平台供应商报名响应，采购决策模块是医院人员通过采购决策工具，查询产品、价格、供应商等信息，提供医院人员科学决策。", "is_potential_toc": true}, {"table_index": 2, "row": 3, "cell": 2, "text": "望海康信易普优采平台于 2019 年4 月正式上线，隶属于望海康信（北京）科技股份公司。望海康信易普优采平台业务主要包含：阳光招采、市场调研、采购决策等业务，其中：阳光招采模块是医院发布采购信息，采购信息高效同步到平台，供应商根据采购信息影响报价；市场调研模块是医院发布采购调研公告到平台，平台供应商报名响应，采购决策模块是医院人员通过采购决策工具，查询产品、价格、供应商等信息，提供医院人员科学决策。", "is_potential_toc": true}, {"table_index": 2, "row": 3, "cell": 3, "text": "望海康信易普优采平台于 2019 年4 月正式上线，隶属于望海康信（北京）科技股份公司。望海康信易普优采平台业务主要包含：阳光招采、市场调研、采购决策等业务，其中：阳光招采模块是医院发布采购信息，采购信息高效同步到平台，供应商根据采购信息影响报价；市场调研模块是医院发布采购调研公告到平台，平台供应商报名响应，采购决策模块是医院人员通过采购决策工具，查询产品、价格、供应商等信息，提供医院人员科学决策。", "is_potential_toc": true}, {"table_index": 2, "row": 3, "cell": 4, "text": "望海康信易普优采平台于 2019 年4 月正式上线，隶属于望海康信（北京）科技股份公司。望海康信易普优采平台业务主要包含：阳光招采、市场调研、采购决策等业务，其中：阳光招采模块是医院发布采购信息，采购信息高效同步到平台，供应商根据采购信息影响报价；市场调研模块是医院发布采购调研公告到平台，平台供应商报名响应，采购决策模块是医院人员通过采购决策工具，查询产品、价格、供应商等信息，提供医院人员科学决策。", "is_potential_toc": true}, {"table_index": 2, "row": 3, "cell": 5, "text": "望海康信易普优采平台于 2019 年4 月正式上线，隶属于望海康信（北京）科技股份公司。望海康信易普优采平台业务主要包含：阳光招采、市场调研、采购决策等业务，其中：阳光招采模块是医院发布采购信息，采购信息高效同步到平台，供应商根据采购信息影响报价；市场调研模块是医院发布采购调研公告到平台，平台供应商报名响应，采购决策模块是医院人员通过采购决策工具，查询产品、价格、供应商等信息，提供医院人员科学决策。", "is_potential_toc": true}, {"table_index": 2, "row": 4, "cell": 1, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信易普优采平台的网络架构、系统管理软件、主机操作系统、数据库管理系统、业务应用系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备网络架构、通信传输、访问控制、入侵防范、访问控制、恶意代码防范、身份鉴别、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、自行软件开发、测试验收、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理等安全管理措施。\n本次安全等级测评共发现望海康信易普优采平台存在高风险问题0个，中风险问题20个，低风险问题13个。望海康信易普优采平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 2, "row": 4, "cell": 2, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信易普优采平台的网络架构、系统管理软件、主机操作系统、数据库管理系统、业务应用系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备网络架构、通信传输、访问控制、入侵防范、访问控制、恶意代码防范、身份鉴别、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、自行软件开发、测试验收、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理等安全管理措施。\n本次安全等级测评共发现望海康信易普优采平台存在高风险问题0个，中风险问题20个，低风险问题13个。望海康信易普优采平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 2, "row": 4, "cell": 3, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信易普优采平台的网络架构、系统管理软件、主机操作系统、数据库管理系统、业务应用系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备网络架构、通信传输、访问控制、入侵防范、访问控制、恶意代码防范、身份鉴别、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、自行软件开发、测试验收、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理等安全管理措施。\n本次安全等级测评共发现望海康信易普优采平台存在高风险问题0个，中风险问题20个，低风险问题13个。望海康信易普优采平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 2, "row": 4, "cell": 4, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信易普优采平台的网络架构、系统管理软件、主机操作系统、数据库管理系统、业务应用系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备网络架构、通信传输、访问控制、入侵防范、访问控制、恶意代码防范、身份鉴别、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、自行软件开发、测试验收、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理等安全管理措施。\n本次安全等级测评共发现望海康信易普优采平台存在高风险问题0个，中风险问题20个，低风险问题13个。望海康信易普优采平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 2, "row": 4, "cell": 5, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信易普优采平台的网络架构、系统管理软件、主机操作系统、数据库管理系统、业务应用系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备网络架构、通信传输、访问控制、入侵防范、访问控制、恶意代码防范、身份鉴别、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、自行软件开发、测试验收、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理等安全管理措施。\n本次安全等级测评共发现望海康信易普优采平台存在高风险问题0个，中风险问题20个，低风险问题13个。望海康信易普优采平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 3, "row": 1, "cell": 1, "text": "云计算平台\n云服务客户业务应用系统(平台报告编号：11010113074-20002-25-0019-01）", "is_potential_toc": true}, {"table_index": 3, "row": 1, "cell": 2, "text": "云计算平台\n云服务客户业务应用系统(平台报告编号：11010113074-20002-25-0019-01）", "is_potential_toc": true}, {"table_index": 3, "row": 1, "cell": 3, "text": "云计算平台\n云服务客户业务应用系统(平台报告编号：11010113074-20002-25-0019-01）", "is_potential_toc": true}, {"table_index": 3, "row": 1, "cell": 4, "text": "云计算平台\n云服务客户业务应用系统(平台报告编号：11010113074-20002-25-0019-01）", "is_potential_toc": true}, {"table_index": 3, "row": 1, "cell": 5, "text": "云计算平台\n云服务客户业务应用系统(平台报告编号：11010113074-20002-25-0019-01）", "is_potential_toc": true}, {"table_index": 8, "row": 1, "cell": 1, "text": "ha-03", "is_potential_toc": true}, {"table_index": 8, "row": 1, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 2, "cell": 1, "text": "ha-04", "is_potential_toc": true}, {"table_index": 8, "row": 2, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 3, "cell": 1, "text": "k8s-43", "is_potential_toc": true}, {"table_index": 8, "row": 3, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 4, "cell": 1, "text": "k8s-44", "is_potential_toc": true}, {"table_index": 8, "row": 4, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 5, "cell": 1, "text": "ceph-00", "is_potential_toc": true}, {"table_index": 8, "row": 5, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 6, "cell": 1, "text": "ceph-08", "is_potential_toc": true}, {"table_index": 8, "row": 6, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 7, "cell": 1, "text": "db-84", "is_potential_toc": true}, {"table_index": 8, "row": 7, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 7, "cell": 5, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 8, "row": 8, "cell": 1, "text": "db-85", "is_potential_toc": true}, {"table_index": 8, "row": 8, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 8, "cell": 5, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 8, "row": 9, "cell": 1, "text": "efk-03", "is_potential_toc": true}, {"table_index": 8, "row": 9, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 10, "cell": 1, "text": "efk-04", "is_potential_toc": true}, {"table_index": 8, "row": 10, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 11, "cell": 1, "text": "mq-03", "is_potential_toc": true}, {"table_index": 8, "row": 11, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 11, "cell": 6, "text": "rabbitmq 3.6.14", "is_potential_toc": true}, {"table_index": 8, "row": 12, "cell": 1, "text": "mq-04", "is_potential_toc": true}, {"table_index": 8, "row": 12, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 12, "cell": 6, "text": "rabbitmq 3.6.14", "is_potential_toc": true}, {"table_index": 8, "row": 13, "cell": 4, "text": "CentOS 7.9", "is_potential_toc": true}, {"table_index": 8, "row": 14, "cell": 1, "text": "slb-03", "is_potential_toc": true}, {"table_index": 8, "row": 14, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 14, "cell": 6, "text": "Nginx 1.26.2", "is_potential_toc": true}, {"table_index": 8, "row": 15, "cell": 1, "text": "slb-04", "is_potential_toc": true}, {"table_index": 8, "row": 15, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 8, "row": 15, "cell": 6, "text": "Nginx 1.26.2", "is_potential_toc": true}, {"table_index": 9, "row": 1, "cell": 3, "text": "Windows10", "is_potential_toc": true}, {"table_index": 10, "row": 1, "cell": 1, "text": "rabbitmq-1", "is_potential_toc": true}, {"table_index": 10, "row": 1, "cell": 3, "text": "rabbitmq 3.6.14", "is_potential_toc": true}, {"table_index": 10, "row": 1, "cell": 4, "text": "mq-03", "is_potential_toc": true}, {"table_index": 10, "row": 2, "cell": 1, "text": "rabbitmq-2", "is_potential_toc": true}, {"table_index": 10, "row": 2, "cell": 3, "text": "rabbitmq 3.6.14", "is_potential_toc": true}, {"table_index": 10, "row": 2, "cell": 4, "text": "mq-04", "is_potential_toc": true}, {"table_index": 10, "row": 3, "cell": 1, "text": "Nginx-1", "is_potential_toc": true}, {"table_index": 10, "row": 3, "cell": 3, "text": "Nginx 1.26.2", "is_potential_toc": true}, {"table_index": 10, "row": 3, "cell": 4, "text": "slb-03", "is_potential_toc": true}, {"table_index": 10, "row": 4, "cell": 1, "text": "Nginx-2", "is_potential_toc": true}, {"table_index": 10, "row": 4, "cell": 3, "text": "Nginx 1.26.2", "is_potential_toc": true}, {"table_index": 10, "row": 4, "cell": 4, "text": "slb-04", "is_potential_toc": true}, {"table_index": 10, "row": 5, "cell": 1, "text": "MySQL数据库1", "is_potential_toc": true}, {"table_index": 10, "row": 5, "cell": 3, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 10, "row": 5, "cell": 4, "text": "db-84", "is_potential_toc": true}, {"table_index": 10, "row": 6, "cell": 1, "text": "MySQL数据库2", "is_potential_toc": true}, {"table_index": 10, "row": 6, "cell": 3, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 10, "row": 6, "cell": 4, "text": "db-85", "is_potential_toc": true}, {"table_index": 11, "row": 1, "cell": 3, "text": "V1.1.0.29", "is_potential_toc": true}, {"table_index": 11, "row": 2, "cell": 3, "text": "V1.1.0.29", "is_potential_toc": true}, {"table_index": 13, "row": 1, "cell": 3, "text": "18611388445", "is_potential_toc": true}, {"table_index": 13, "row": 2, "cell": 3, "text": "13889675212", "is_potential_toc": true}, {"table_index": 13, "row": 3, "cell": 3, "text": "13910031008", "is_potential_toc": true}, {"table_index": 15, "row": 2, "cell": 1, "text": "ha-03", "is_potential_toc": true}, {"table_index": 15, "row": 2, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 3, "cell": 1, "text": "ha-04", "is_potential_toc": true}, {"table_index": 15, "row": 3, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 4, "cell": 1, "text": "k8s-43", "is_potential_toc": true}, {"table_index": 15, "row": 4, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 5, "cell": 1, "text": "k8s-44", "is_potential_toc": true}, {"table_index": 15, "row": 5, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 6, "cell": 1, "text": "k8s-46", "is_potential_toc": true}, {"table_index": 15, "row": 6, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 7, "cell": 1, "text": "ceph-00", "is_potential_toc": true}, {"table_index": 15, "row": 7, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 8, "cell": 1, "text": "ceph-08", "is_potential_toc": true}, {"table_index": 15, "row": 8, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 9, "cell": 1, "text": "db-84", "is_potential_toc": true}, {"table_index": 15, "row": 9, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 10, "cell": 1, "text": "db-85", "is_potential_toc": true}, {"table_index": 15, "row": 10, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 11, "cell": 1, "text": "db-86", "is_potential_toc": true}, {"table_index": 15, "row": 11, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 12, "cell": 1, "text": "efk-03", "is_potential_toc": true}, {"table_index": 15, "row": 12, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 13, "cell": 1, "text": "efk-04", "is_potential_toc": true}, {"table_index": 15, "row": 13, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 14, "cell": 1, "text": "mq-03", "is_potential_toc": true}, {"table_index": 15, "row": 14, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 15, "cell": 1, "text": "mq-04", "is_potential_toc": true}, {"table_index": 15, "row": 15, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 16, "cell": 2, "text": "CentOS 7.9", "is_potential_toc": true}, {"table_index": 15, "row": 17, "cell": 1, "text": "slb-03", "is_potential_toc": true}, {"table_index": 15, "row": 17, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 18, "cell": 1, "text": "slb-04", "is_potential_toc": true}, {"table_index": 15, "row": 18, "cell": 2, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 15, "row": 19, "cell": 2, "text": "window10", "is_potential_toc": true}, {"table_index": 17, "row": 1, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 17, "row": 2, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 17, "row": 3, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 17, "row": 4, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 17, "row": 5, "cell": 2, "text": "db-84,db-85,db-86,mq-03,mq-04", "is_potential_toc": true}, {"table_index": 18, "row": 1, "cell": 0, "text": "绿盟远程安全评估系统V6.0", "is_potential_toc": true}, {"table_index": 20, "row": 5, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04", "is_potential_toc": true}, {"table_index": 20, "row": 6, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 20, "row": 7, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2", "is_potential_toc": true}, {"table_index": 20, "row": 8, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 20, "row": 9, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 20, "row": 10, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端", "is_potential_toc": true}, {"table_index": 20, "row": 11, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04", "is_potential_toc": true}, {"table_index": 20, "row": 12, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 20, "row": 13, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台、业务数据、个人数据", "is_potential_toc": true}, {"table_index": 20, "row": 14, "cell": 2, "text": "MySQL数据库1、MySQL数据库2", "is_potential_toc": true}, {"table_index": 20, "row": 16, "cell": 2, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、slb-03、slb-04、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台、业务数据、个人数据、备份服务器", "is_potential_toc": true}, {"table_index": 21, "row": 5, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04", "is_potential_toc": true}, {"table_index": 21, "row": 6, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 21, "row": 7, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2", "is_potential_toc": true}, {"table_index": 21, "row": 8, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 21, "row": 9, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 21, "row": 10, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端", "is_potential_toc": true}, {"table_index": 21, "row": 11, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04", "is_potential_toc": true}, {"table_index": 21, "row": 12, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 21, "row": 13, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台、业务数据、个人数据", "is_potential_toc": true}, {"table_index": 21, "row": 14, "cell": 3, "text": "MySQL数据库1、MySQL数据库2", "is_potential_toc": true}, {"table_index": 21, "row": 16, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、slb-03、slb-04、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台、业务数据、个人数据、备份服务器", "is_potential_toc": true}, {"table_index": 22, "row": 12, "cell": 3, "text": "81.87", "is_potential_toc": true}, {"table_index": 22, "row": 12, "cell": 4, "text": "81.87", "is_potential_toc": true}, {"table_index": 22, "row": 12, "cell": 5, "text": "81.87", "is_potential_toc": true}, {"table_index": 22, "row": 12, "cell": 6, "text": "81.87", "is_potential_toc": true}, {"table_index": 23, "row": 1, "cell": 1, "text": "被测系统符合率高于90%，且无重大风险隐患。", "is_potential_toc": true}, {"table_index": 23, "row": 2, "cell": 1, "text": "被测系统符合率高于60%，低于90%；或者符合率为[90%, 100%]且存在重大风险隐患。", "is_potential_toc": true}, {"table_index": 23, "row": 3, "cell": 1, "text": "被测系统符合率低于60%。", "is_potential_toc": true}, {"table_index": 24, "row": 4, "cell": 4, "text": "建议设置口令更换周期小于90天。", "is_potential_toc": true}, {"table_index": 24, "row": 5, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04", "is_potential_toc": true}, {"table_index": 24, "row": 5, "cell": 4, "text": "建议设置登录连接超时自动退出时长小于15分钟。", "is_potential_toc": true}, {"table_index": 24, "row": 6, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 24, "row": 7, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2", "is_potential_toc": true}, {"table_index": 24, "row": 8, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 24, "row": 9, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 24, "row": 10, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端", "is_potential_toc": true}, {"table_index": 24, "row": 11, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04", "is_potential_toc": true}, {"table_index": 24, "row": 12, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、运维终端、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台", "is_potential_toc": true}, {"table_index": 24, "row": 13, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、备份服务器、slb-03、slb-04、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台、业务数据、个人数据", "is_potential_toc": true}, {"table_index": 24, "row": 14, "cell": 3, "text": "MySQL数据库1、MySQL数据库2", "is_potential_toc": true}, {"table_index": 24, "row": 16, "cell": 3, "text": "ha-03、ha-04、k8s-43、k8s-44、ceph-00、ceph-08、db-84、db-85、efk-03、efk-04、mq-03、mq-04、slb-03、slb-04、rabbitmq-1、rabbitmq-2、Nginx-1、Nginx-2、MySQL数据库1、MySQL数据库2、望海康信易普优采平台-客户端、望海康信易普优采平台-运营平台、业务数据、个人数据、备份服务器", "is_potential_toc": true}, {"table_index": 27, "row": 1, "cell": 3, "text": "linux V2.11.1", "is_potential_toc": true}, {"table_index": 27, "row": 1, "cell": 4, "text": "JumpServer FIT2CLOUD", "is_potential_toc": true}, {"table_index": 27, "row": 1, "cell": 7, "text": "10.10.*.243", "is_potential_toc": true}, {"table_index": 27, "row": 2, "cell": 3, "text": "VRP V500R001C60SPC300", "is_potential_toc": true}, {"table_index": 27, "row": 2, "cell": 4, "text": "华为 USG6650", "is_potential_toc": true}, {"table_index": 27, "row": 2, "cell": 7, "text": "10.10.*.2", "is_potential_toc": true}, {"table_index": 27, "row": 3, "cell": 3, "text": "VRP V500R001C60SPC300", "is_potential_toc": true}, {"table_index": 27, "row": 3, "cell": 4, "text": "华为 USG6650", "is_potential_toc": true}, {"table_index": 27, "row": 3, "cell": 7, "text": "10.10.*.3", "is_potential_toc": true}, {"table_index": 27, "row": 4, "cell": 1, "text": "VPN（双活负载均衡模式）-1", "is_potential_toc": true}, {"table_index": 27, "row": 4, "cell": 3, "text": "SSL M7.6.8", "is_potential_toc": true}, {"table_index": 27, "row": 4, "cell": 4, "text": "深信服 VPN-1000", "is_potential_toc": true}, {"table_index": 27, "row": 4, "cell": 7, "text": "10.10.*.4", "is_potential_toc": true}, {"table_index": 27, "row": 5, "cell": 1, "text": "VPN（双活负载均衡模式）-2", "is_potential_toc": true}, {"table_index": 27, "row": 5, "cell": 3, "text": "SSL M7.6.8", "is_potential_toc": true}, {"table_index": 27, "row": 5, "cell": 4, "text": "深信服 VPN-1000", "is_potential_toc": true}, {"table_index": 27, "row": 5, "cell": 7, "text": "10.10.*.4", "is_potential_toc": true}, {"table_index": 27, "row": 7, "cell": 3, "text": "About GSA V9.0.1", "is_potential_toc": true}, {"table_index": 27, "row": 8, "cell": 3, "text": "V3.4.10", "is_potential_toc": true}, {"table_index": 28, "row": 1, "cell": 1, "text": "ha-03", "is_potential_toc": true}, {"table_index": 28, "row": 1, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 1, "cell": 8, "text": "10.10.*.27", "is_potential_toc": true}, {"table_index": 28, "row": 2, "cell": 1, "text": "ha-04", "is_potential_toc": true}, {"table_index": 28, "row": 2, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 2, "cell": 8, "text": "10.10.*.28", "is_potential_toc": true}, {"table_index": 28, "row": 3, "cell": 1, "text": "k8s-43", "is_potential_toc": true}, {"table_index": 28, "row": 3, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 3, "cell": 8, "text": "10.10.*.143", "is_potential_toc": true}, {"table_index": 28, "row": 4, "cell": 1, "text": "k8s-44", "is_potential_toc": true}, {"table_index": 28, "row": 4, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 4, "cell": 8, "text": "10.10.*.144", "is_potential_toc": true}, {"table_index": 28, "row": 5, "cell": 1, "text": "k8s-46", "is_potential_toc": true}, {"table_index": 28, "row": 5, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 5, "cell": 8, "text": "10.10.*.146", "is_potential_toc": true}, {"table_index": 28, "row": 6, "cell": 1, "text": "ceph-00", "is_potential_toc": true}, {"table_index": 28, "row": 6, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 6, "cell": 8, "text": "10.10.*.40", "is_potential_toc": true}, {"table_index": 28, "row": 7, "cell": 1, "text": "ceph-08", "is_potential_toc": true}, {"table_index": 28, "row": 7, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 7, "cell": 8, "text": "10.10.*.48", "is_potential_toc": true}, {"table_index": 28, "row": 8, "cell": 1, "text": "db-84", "is_potential_toc": true}, {"table_index": 28, "row": 8, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 8, "cell": 5, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 28, "row": 8, "cell": 8, "text": "10.10.*.84", "is_potential_toc": true}, {"table_index": 28, "row": 9, "cell": 1, "text": "db-85", "is_potential_toc": true}, {"table_index": 28, "row": 9, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 9, "cell": 5, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 28, "row": 9, "cell": 8, "text": "10.10.*.85", "is_potential_toc": true}, {"table_index": 28, "row": 10, "cell": 1, "text": "db-86", "is_potential_toc": true}, {"table_index": 28, "row": 10, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 10, "cell": 5, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 28, "row": 10, "cell": 8, "text": "10.10.*.86", "is_potential_toc": true}, {"table_index": 28, "row": 11, "cell": 1, "text": "efk-03", "is_potential_toc": true}, {"table_index": 28, "row": 11, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 11, "cell": 8, "text": "10.10.*.73", "is_potential_toc": true}, {"table_index": 28, "row": 12, "cell": 1, "text": "efk-04", "is_potential_toc": true}, {"table_index": 28, "row": 12, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 12, "cell": 8, "text": "10.10.*.74", "is_potential_toc": true}, {"table_index": 28, "row": 13, "cell": 1, "text": "mq-03", "is_potential_toc": true}, {"table_index": 28, "row": 13, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 13, "cell": 6, "text": "rabbitmq 3.6.14", "is_potential_toc": true}, {"table_index": 28, "row": 13, "cell": 8, "text": "10.10.*.87", "is_potential_toc": true}, {"table_index": 28, "row": 14, "cell": 1, "text": "mq-04", "is_potential_toc": true}, {"table_index": 28, "row": 14, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 14, "cell": 6, "text": "rabbitmq 3.6.14", "is_potential_toc": true}, {"table_index": 28, "row": 14, "cell": 8, "text": "10.10.*.88", "is_potential_toc": true}, {"table_index": 28, "row": 15, "cell": 4, "text": "CentOS 7.9", "is_potential_toc": true}, {"table_index": 28, "row": 15, "cell": 8, "text": "192.168.*.120", "is_potential_toc": true}, {"table_index": 28, "row": 16, "cell": 1, "text": "slb-03", "is_potential_toc": true}, {"table_index": 28, "row": 16, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 16, "cell": 6, "text": "Nginx 1.26.2", "is_potential_toc": true}, {"table_index": 28, "row": 16, "cell": 8, "text": "10.10.*.23", "is_potential_toc": true}, {"table_index": 28, "row": 17, "cell": 1, "text": "slb-04", "is_potential_toc": true}, {"table_index": 28, "row": 17, "cell": 4, "text": "CentOS 7.6", "is_potential_toc": true}, {"table_index": 28, "row": 17, "cell": 6, "text": "Nginx 1.26.2", "is_potential_toc": true}, {"table_index": 28, "row": 17, "cell": 8, "text": "10.10.*.24", "is_potential_toc": true}, {"table_index": 29, "row": 1, "cell": 3, "text": "Windows10", "is_potential_toc": true}, {"table_index": 29, "row": 1, "cell": 6, "text": "192.168.*.11", "is_potential_toc": true}, {"table_index": 31, "row": 1, "cell": 6, "text": "10.10.*.40", "is_potential_toc": true}, {"table_index": 31, "row": 2, "cell": 1, "text": "rabbitmq-1", "is_potential_toc": true}, {"table_index": 31, "row": 2, "cell": 2, "text": "mq-03", "is_potential_toc": true}, {"table_index": 31, "row": 2, "cell": 3, "text": "rabbitmq 3.6.14", "is_potential_toc": true}, {"table_index": 31, "row": 2, "cell": 6, "text": "10.10.*.87", "is_potential_toc": true}, {"table_index": 31, "row": 3, "cell": 1, "text": "rabbitmq-2", "is_potential_toc": true}, {"table_index": 31, "row": 3, "cell": 2, "text": "mq-04", "is_potential_toc": true}, {"table_index": 31, "row": 3, "cell": 3, "text": "rabbitmq 3.6.14", "is_potential_toc": true}, {"table_index": 31, "row": 3, "cell": 6, "text": "10.10.*.88", "is_potential_toc": true}, {"table_index": 31, "row": 4, "cell": 1, "text": "Nginx-1", "is_potential_toc": true}, {"table_index": 31, "row": 4, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 31, "row": 4, "cell": 3, "text": "Nginx 1.26.2", "is_potential_toc": true}, {"table_index": 31, "row": 5, "cell": 1, "text": "Nginx-2", "is_potential_toc": true}, {"table_index": 31, "row": 5, "cell": 2, "text": "slb-04", "is_potential_toc": true}, {"table_index": 31, "row": 5, "cell": 3, "text": "Nginx 1.26.2", "is_potential_toc": true}, {"table_index": 31, "row": 6, "cell": 1, "text": "MySQL数据库1", "is_potential_toc": true}, {"table_index": 31, "row": 6, "cell": 2, "text": "db-84", "is_potential_toc": true}, {"table_index": 31, "row": 6, "cell": 3, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 31, "row": 6, "cell": 6, "text": "10.10.*.84", "is_potential_toc": true}, {"table_index": 31, "row": 7, "cell": 1, "text": "MySQL数据库2", "is_potential_toc": true}, {"table_index": 31, "row": 7, "cell": 2, "text": "db-85", "is_potential_toc": true}, {"table_index": 31, "row": 7, "cell": 3, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 31, "row": 7, "cell": 6, "text": "10.10.*.85", "is_potential_toc": true}, {"table_index": 31, "row": 8, "cell": 1, "text": "MySQL数据库3", "is_potential_toc": true}, {"table_index": 31, "row": 8, "cell": 2, "text": "db-86", "is_potential_toc": true}, {"table_index": 31, "row": 8, "cell": 3, "text": "MySQL 5.7", "is_potential_toc": true}, {"table_index": 31, "row": 8, "cell": 6, "text": "10.10.*.86", "is_potential_toc": true}, {"table_index": 32, "row": 1, "cell": 3, "text": "V1.1.0.29", "is_potential_toc": true}, {"table_index": 32, "row": 2, "cell": 3, "text": "V1.1.0.29", "is_potential_toc": true}, {"table_index": 34, "row": 1, "cell": 3, "text": "GM004410620210371", "is_potential_toc": true}, {"table_index": 34, "row": 1, "cell": 4, "text": "SM2，SM3，SM4", "is_potential_toc": true}, {"table_index": 35, "row": 1, "cell": 3, "text": "18611388445", "is_potential_toc": true}, {"table_index": 35, "row": 2, "cell": 3, "text": "13889675212", "is_potential_toc": true}, {"table_index": 35, "row": 3, "cell": 3, "text": "13910031008", "is_potential_toc": true}, {"table_index": 37, "row": 8, "cell": 3, "text": "MySQL设置数据库置登录超时限制600S。", "is_potential_toc": true}, {"table_index": 37, "row": 9, "cell": 3, "text": "部分整改，望海康信易普优采平台-客户端通过用户名+口令进行首次身份验证，再通过微信扫描页面二维码随机生成6位安全码输入二次验证，已实现两种身份鉴别方式。", "is_potential_toc": true}, {"table_index": 37, "row": 13, "cell": 1, "text": "本地留存有6个月以上的日志记录，但未配置日志服务器。", "is_potential_toc": true}, {"table_index": 37, "row": 23, "cell": 3, "text": "每年对安全管理制度的合理性和适用性进行审定，提供了制度修订会议纪要《2024年安全管理制度修订及评审》。", "is_potential_toc": true}, {"table_index": 37, "row": 25, "cell": 3, "text": "每年针对不同岗位的人员至少进行一次技能考核，提供了公司员工的考核记录《信息安全考核20241111》。", "is_potential_toc": true}, {"table_index": 37, "row": 27, "cell": 3, "text": "被测系统采用的密码产品为深信服 aTrust-1000-S1060G VPN，具有国家密码局颁发的商用密码产品型号证书，证书编号为GM004410620210371。", "is_potential_toc": true}, {"table_index": 37, "row": 37, "cell": 3, "text": "所使用密码产品为深信服 aTrust-1000-S1060G VPN，已经过国家密码管理主管部门认证核准。", "is_potential_toc": true}, {"table_index": 37, "row": 38, "cell": 3, "text": "每年对应急预案进行评估修订，提供了《2024年安全管理制度修订及评审》，内容包括修订内容，修订时间等。", "is_potential_toc": true}, {"table_index": 42, "row": 2, "cell": 1, "text": "ha-03", "is_potential_toc": true}, {"table_index": 42, "row": 3, "cell": 1, "text": "ha-03", "is_potential_toc": true}, {"table_index": 42, "row": 4, "cell": 1, "text": "ha-03", "is_potential_toc": true}, {"table_index": 42, "row": 5, "cell": 1, "text": "ha-03", "is_potential_toc": true}, {"table_index": 42, "row": 6, "cell": 1, "text": "ha-04", "is_potential_toc": true}, {"table_index": 42, "row": 7, "cell": 1, "text": "ha-04", "is_potential_toc": true}, {"table_index": 42, "row": 8, "cell": 1, "text": "ha-04", "is_potential_toc": true}, {"table_index": 42, "row": 9, "cell": 1, "text": "ha-04", "is_potential_toc": true}, {"table_index": 42, "row": 10, "cell": 1, "text": "k8s-43", "is_potential_toc": true}, {"table_index": 42, "row": 11, "cell": 1, "text": "k8s-43", "is_potential_toc": true}, {"table_index": 42, "row": 12, "cell": 1, "text": "k8s-43", "is_potential_toc": true}, {"table_index": 42, "row": 13, "cell": 1, "text": "k8s-43", "is_potential_toc": true}, {"table_index": 42, "row": 14, "cell": 1, "text": "k8s-44", "is_potential_toc": true}, {"table_index": 42, "row": 15, "cell": 1, "text": "k8s-44", "is_potential_toc": true}, {"table_index": 42, "row": 16, "cell": 1, "text": "k8s-44", "is_potential_toc": true}, {"table_index": 42, "row": 17, "cell": 1, "text": "k8s-44", "is_potential_toc": true}, {"table_index": 42, "row": 18, "cell": 1, "text": "ceph-00", "is_potential_toc": true}, {"table_index": 42, "row": 19, "cell": 1, "text": "ceph-00", "is_potential_toc": true}, {"table_index": 42, "row": 20, "cell": 1, "text": "ceph-00", "is_potential_toc": true}, {"table_index": 42, "row": 21, "cell": 1, "text": "ceph-00", "is_potential_toc": true}, {"table_index": 42, "row": 22, "cell": 1, "text": "ceph-08", "is_potential_toc": true}, {"table_index": 42, "row": 23, "cell": 1, "text": "ceph-08", "is_potential_toc": true}, {"table_index": 42, "row": 24, "cell": 1, "text": "ceph-08", "is_potential_toc": true}, {"table_index": 42, "row": 25, "cell": 1, "text": "ceph-08", "is_potential_toc": true}, {"table_index": 42, "row": 26, "cell": 1, "text": "db-84", "is_potential_toc": true}, {"table_index": 42, "row": 27, "cell": 1, "text": "db-84", "is_potential_toc": true}, {"table_index": 42, "row": 28, "cell": 1, "text": "db-84", "is_potential_toc": true}, {"table_index": 42, "row": 29, "cell": 1, "text": "db-84", "is_potential_toc": true}, {"table_index": 42, "row": 30, "cell": 1, "text": "db-85", "is_potential_toc": true}, {"table_index": 42, "row": 31, "cell": 1, "text": "db-85", "is_potential_toc": true}, {"table_index": 42, "row": 32, "cell": 1, "text": "db-85", "is_potential_toc": true}, {"table_index": 42, "row": 33, "cell": 1, "text": "db-85", "is_potential_toc": true}, {"table_index": 42, "row": 34, "cell": 1, "text": "efk-03", "is_potential_toc": true}, {"table_index": 42, "row": 35, "cell": 1, "text": "efk-03", "is_potential_toc": true}, {"table_index": 42, "row": 36, "cell": 1, "text": "efk-03", "is_potential_toc": true}, {"table_index": 42, "row": 37, "cell": 1, "text": "efk-03", "is_potential_toc": true}, {"table_index": 42, "row": 38, "cell": 1, "text": "efk-04", "is_potential_toc": true}, {"table_index": 42, "row": 39, "cell": 1, "text": "efk-04", "is_potential_toc": true}, {"table_index": 42, "row": 40, "cell": 1, "text": "efk-04", "is_potential_toc": true}, {"table_index": 42, "row": 41, "cell": 1, "text": "efk-04", "is_potential_toc": true}, {"table_index": 42, "row": 42, "cell": 1, "text": "mq-03", "is_potential_toc": true}, {"table_index": 42, "row": 43, "cell": 1, "text": "mq-03", "is_potential_toc": true}, {"table_index": 42, "row": 44, "cell": 1, "text": "mq-03", "is_potential_toc": true}, {"table_index": 42, "row": 45, "cell": 1, "text": "mq-03", "is_potential_toc": true}, {"table_index": 42, "row": 46, "cell": 1, "text": "mq-04", "is_potential_toc": true}, {"table_index": 42, "row": 47, "cell": 1, "text": "mq-04", "is_potential_toc": true}, {"table_index": 42, "row": 48, "cell": 1, "text": "mq-04", "is_potential_toc": true}, {"table_index": 42, "row": 49, "cell": 1, "text": "mq-04", "is_potential_toc": true}, {"table_index": 42, "row": 54, "cell": 1, "text": "slb-03", "is_potential_toc": true}, {"table_index": 42, "row": 55, "cell": 1, "text": "slb-03", "is_potential_toc": true}, {"table_index": 42, "row": 56, "cell": 1, "text": "slb-03", "is_potential_toc": true}, {"table_index": 42, "row": 57, "cell": 1, "text": "slb-03", "is_potential_toc": true}, {"table_index": 42, "row": 58, "cell": 1, "text": "slb-04", "is_potential_toc": true}, {"table_index": 42, "row": 59, "cell": 1, "text": "slb-04", "is_potential_toc": true}, {"table_index": 42, "row": 60, "cell": 1, "text": "slb-04", "is_potential_toc": true}, {"table_index": 42, "row": 61, "cell": 1, "text": "slb-04", "is_potential_toc": true}, {"table_index": 43, "row": 2, "cell": 1, "text": "rabbitmq-1", "is_potential_toc": true}, {"table_index": 43, "row": 3, "cell": 1, "text": "rabbitmq-1", "is_potential_toc": true}, {"table_index": 43, "row": 4, "cell": 1, "text": "rabbitmq-1", "is_potential_toc": true}, {"table_index": 43, "row": 5, "cell": 1, "text": "rabbitmq-1", "is_potential_toc": true}, {"table_index": 43, "row": 6, "cell": 1, "text": "rabbitmq-2", "is_potential_toc": true}, {"table_index": 43, "row": 7, "cell": 1, "text": "rabbitmq-2", "is_potential_toc": true}, {"table_index": 43, "row": 8, "cell": 1, "text": "rabbitmq-2", "is_potential_toc": true}, {"table_index": 43, "row": 9, "cell": 1, "text": "rabbitmq-2", "is_potential_toc": true}, {"table_index": 43, "row": 10, "cell": 1, "text": "Nginx-1", "is_potential_toc": true}, {"table_index": 43, "row": 11, "cell": 1, "text": "Nginx-1", "is_potential_toc": true}, {"table_index": 43, "row": 12, "cell": 1, "text": "Nginx-1", "is_potential_toc": true}, {"table_index": 43, "row": 13, "cell": 1, "text": "Nginx-1", "is_potential_toc": true}, {"table_index": 43, "row": 14, "cell": 1, "text": "Nginx-2", "is_potential_toc": true}, {"table_index": 43, "row": 15, "cell": 1, "text": "Nginx-2", "is_potential_toc": true}, {"table_index": 43, "row": 16, "cell": 1, "text": "Nginx-2", "is_potential_toc": true}, {"table_index": 43, "row": 17, "cell": 1, "text": "Nginx-2", "is_potential_toc": true}, {"table_index": 43, "row": 18, "cell": 1, "text": "MySQL数据库1", "is_potential_toc": true}, {"table_index": 43, "row": 19, "cell": 1, "text": "MySQL数据库1", "is_potential_toc": true}, {"table_index": 43, "row": 20, "cell": 1, "text": "MySQL数据库1", "is_potential_toc": true}, {"table_index": 43, "row": 21, "cell": 1, "text": "MySQL数据库1", "is_potential_toc": true}, {"table_index": 43, "row": 22, "cell": 1, "text": "MySQL数据库2", "is_potential_toc": true}, {"table_index": 43, "row": 23, "cell": 1, "text": "MySQL数据库2", "is_potential_toc": true}, {"table_index": 43, "row": 24, "cell": 1, "text": "MySQL数据库2", "is_potential_toc": true}, {"table_index": 43, "row": 25, "cell": 1, "text": "MySQL数据库2", "is_potential_toc": true}, {"table_index": 56, "row": 1, "cell": 2, "text": "经访谈及核查，系统部署在私有云望海康信云平台，被测系统采用了少许虚拟服务器，经现场查看zabbix监控数据以及设备运行情况，设备CPU、内存和存储等资源使用率均不超90%，在日常使用中也未曾出现由于资源配置等原因导致业务处理能力低下、业务系统中断的现象，业务处理能力可基本满足业务高峰期需要。", "is_potential_toc": true}, {"table_index": 56, "row": 2, "cell": 2, "text": "经访谈及核查，系统部署在私有云望海康信云平台，云计算平台接入电信双条通信线路BGP_80M、BGP_100M，带宽根据需求情况自由分配，通过查看关键节点设备带宽使用率以及查看zabbix监控数据，其接入带宽以及关键节点设备端口带宽使用率不超90%，据访谈管理员，未发现过接入网络存在带宽瓶颈的情况，可满足业务高峰期需要。", "is_potential_toc": true}, {"table_index": 56, "row": 4, "cell": 2, "text": "经现场查看，系统部署在私有云望海康信云平台，网络拓扑图与实际运行环境相符，重要网络区域服务器区部署在网络内部，采用华为 USG6650防火墙设置控制策略、通过云计算平台划分安全组实施访问控制、隔离。", "is_potential_toc": true}, {"table_index": 58, "row": 6, "cell": 2, "text": "经核查，安全策略根据业务部门需求提交工单进行开通，工单上写明有策略使用周期，已达到精细控制，每项服务单独开通，现网已对访问控制策略进行了优化，不定期2-3个月排查不活跃的策略，目前多余或无效的访问控制规则，保证了各访问控制策略数量的最小化。", "is_potential_toc": true}, {"table_index": 58, "row": 9, "cell": 2, "text": "经核查，该系统部署在私有云望海康信云平台，望海康信云平台部署了华为 USG6650下一代防火墙，能够检测到应用层内容，可对进出网络的数据流实现基于应用协议的访问控制，可实现基于应用内容的访问控制。", "is_potential_toc": true}, {"table_index": 58, "row": 11, "cell": 2, "text": "经核查，系统部署在私有云望海康信云平台，提供了防火墙安全服务，该防火墙具有入侵防御模块和防病毒模块，双向引流，在关键网络节点处进行检测、防止和限制从内部发起的网络攻击行为，特征库更新时间20241211，已更新至最新版本。", "is_potential_toc": true}, {"table_index": 58, "row": 14, "cell": 2, "text": "经核查，系统部署在私有云望海康信云平台，提供了防火墙安全服务，该防火墙具有入侵防御模块和防病毒模块，双向引流检测，能够检测到对系统发起的恶意代码攻击并进行清除，特征库更新时间20241218，当前为最新版本。", "is_potential_toc": true}, {"table_index": 58, "row": 18, "cell": 2, "text": "经核查，日志记录只有审计管理员可以进行访问和操作，其他用户无法进行修改，不可对已有记录进行修改，通过ELK日志审计服务备份保留180天以上的审计记录，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 60, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 60, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 61, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 61, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 62, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 62, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 63, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 63, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 64, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 64, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 65, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 65, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 66, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 66, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 67, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 67, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 68, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 68, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 69, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 69, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 70, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 70, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 71, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 71, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 72, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 72, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 73, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 73, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 74, "row": 2, "cell": 2, "text": "经核查，查看profile和system-auth，服务器已配置非法登5次后锁定账户10分钟，配置登录连接超时自动退出时长为 7200s，登录超时时间过长。", "is_potential_toc": true}, {"table_index": 74, "row": 14, "cell": 2, "text": "经核查，服务器审计日志仅授权用户可进行访问，保存周期大于6个月，audit日志每天备份至FTP中，但未对rsyslog日志进行定期备份，无法避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 75, "row": 1, "cell": 2, "text": "经核查，终端采用用户名+口令方式进行登录，经查看本地安全策略，身份标识唯一，无空口令账户，具备口令复杂度要求，口令长度要求8位以上，由大小写字母+数字+特殊字符组成，未设置口令更换周期。", "is_potential_toc": true}, {"table_index": 75, "row": 2, "cell": 2, "text": "经核查，查看本地安全策略，终端已配置非法登8次后锁定账户10分钟，登录链接超时自动退出时间为3分钟。", "is_potential_toc": true}, {"table_index": 75, "row": 14, "cell": 2, "text": "经核查，审计日志仅授权用户可进行访问，保存周期大于6个月，但未对系统日志备份。", "is_potential_toc": true}, {"table_index": 75, "row": 21, "cell": 2, "text": "经核查，安装了火绒杀毒软件，可对主机层恶意代码攻击进行防范，病毒库更新日期2024-12-18，当前病毒库已更至最新。", "is_potential_toc": true}, {"table_index": 76, "row": 14, "cell": 2, "text": "经核查，日志文件权限不高于644，日志本地留存180天，日志备份至ELK日志审计中，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 77, "row": 14, "cell": 2, "text": "经核查，日志文件权限不高于644，日志本地留存180天，日志备份至ELK日志审计中，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 78, "row": 14, "cell": 2, "text": "经核查，日志文件权限不高于644，日志本地留存180天，日志备份至ELK日志审计中，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 79, "row": 14, "cell": 2, "text": "经核查，日志文件权限不高于644，日志本地留存180天，日志备份至ELK日志审计中，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 80, "row": 1, "cell": 2, "text": "经核查，数据库采用用户名+口令方式进行登录，身份标识唯一，无空口令账户，经查看validate_password%和%password_lif%配置，具备口令复杂度策略要求，口令长度要求8位以上，由大小写字母+数字+特殊字符组成，口令更换周期为30天。", "is_potential_toc": true}, {"table_index": 80, "row": 2, "cell": 2, "text": "经核查connection_control%和wait_timeout参数配置，数据库已安装口令插件实现登录失败3次锁定 1000S，设置登录超时限制600S。", "is_potential_toc": true}, {"table_index": 80, "row": 14, "cell": 2, "text": "经核查，数据库审计记录未授权账户无法查看，每天备份至ELK日志审计中，留存时间至少180天，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 80, "row": 20, "cell": 2, "text": "经访谈及核查，目前数据库默认password字段MD5加密存储，MD5算法为不安全算法，且配置数据和审计数据明文存储，无法保证数据在存储过程中的完整性。", "is_potential_toc": true}, {"table_index": 80, "row": 22, "cell": 2, "text": "经访谈及核查，目前数据库默认password字段MD5加密存储，MD5算法为不安全算法，无法保证数据在存储过程中的保密性。", "is_potential_toc": true}, {"table_index": 81, "row": 1, "cell": 2, "text": "经核查，数据库采用用户名+口令方式进行登录，身份标识唯一，无空口令账户，经查看validate_password%和%password_lif%配置，具备口令复杂度策略要求，口令长度要求8位以上，由大小写字母+数字+特殊字符组成，口令更换周期为30天。", "is_potential_toc": true}, {"table_index": 81, "row": 2, "cell": 2, "text": "经核查connection_control%和wait_timeout参数配置，数据库已安装口令插件实现登录失败3次锁定 1000S，设置登录超时限制600S。", "is_potential_toc": true}, {"table_index": 81, "row": 14, "cell": 2, "text": "经核查，数据库审计记录未授权账户无法查看，每天备份至ELK日志审计中，留存时间至少180天，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 81, "row": 20, "cell": 2, "text": "经访谈及核查，目前数据库默认password字段MD5加密存储，MD5算法为不安全算法，且配置数据和审计数据明文存储，无法保证数据在存储过程中的完整性。", "is_potential_toc": true}, {"table_index": 81, "row": 22, "cell": 2, "text": "经访谈及核查，目前数据库默认password字段MD5加密存储，MD5算法为不安全算法，无法保证数据在存储过程中的保密性。", "is_potential_toc": true}, {"table_index": 82, "row": 1, "cell": 2, "text": "经核查，系统采用用户名+口令+微信扫描页面二维码验证方式进行登录，身份标识唯一，无空口令账户，设有口令复杂度策略要求，口令长度8-20位，包含字母和数字，未设置口令更换周期。", "is_potential_toc": true}, {"table_index": 82, "row": 2, "cell": 2, "text": "经核查，系统登录失败三次口令错误需输入图形验证码，10次后停用账号，根据业务需求，超时自动退出时间为30分钟。", "is_potential_toc": true}, {"table_index": 82, "row": 4, "cell": 2, "text": "经核查，系统不涉及大额资金交易，通过用户名+口令进行首次身份验证，再通过微信扫描页面二维码随机生成6位安全码输入二次验证，已实现两种身份鉴别方式。", "is_potential_toc": true}, {"table_index": 82, "row": 14, "cell": 2, "text": "经核查，系统日志存储在ELK日志审计中，未授权人员无法查看，日志可保存180天以上，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 82, "row": 21, "cell": 2, "text": "经核查，系统鉴别数据采用SHA512算法加密，可保证鉴别数据存储过程中的完整性；个人信息和业务数据采用AES算法加密，身份证照片存储在望海康信云平台的对象存储中，对象存储已经开启了服务端加密特性Server Side Encryption，并采用AES256算法加密，配置和审计数据明文存储，未能保证业务数据、个人信息、配置数据和审计数据存储完整性。", "is_potential_toc": true}, {"table_index": 82, "row": 23, "cell": 2, "text": "经核查，系统鉴别数据采用SHA512算法加密，个人信息和业务数据采用AES算法加密，身份证照片存储在望海康信云平台的对象存储中，对象存储已经开启了服务端加密特性Server Side Encryption，并采用AES256算法加密，可保证数据存储过程中的保密性。", "is_potential_toc": true}, {"table_index": 83, "row": 1, "cell": 2, "text": "经核查，系统采用用户名+口令方式进行登录，身份标识唯一，无空口令账户，设有口令复杂度策略要求，口令长度8-20位，包含字母和数字，未设置口令更换周期。", "is_potential_toc": true}, {"table_index": 83, "row": 2, "cell": 2, "text": "经核查，系统登录失败三次口令错误需输入图形验证码，10次后停用账号，根据业务需求，超时自动退出时间为30分钟。", "is_potential_toc": true}, {"table_index": 83, "row": 14, "cell": 2, "text": "经核查，系统日志存储在ELK日志审计中，未授权人员无法查看，日志可保存180天以上，可避免受到未预期的删除、修改或覆盖。", "is_potential_toc": true}, {"table_index": 83, "row": 21, "cell": 2, "text": "经核查，系统鉴别数据采用SHA512算法加密，可保证鉴别数据存储过程中的完整性；个人信息采用AES算法加密，配置和审计数据明文存储，未能保证个人信息、配置数据和审计数据存储完整性。", "is_potential_toc": true}, {"table_index": 83, "row": 23, "cell": 2, "text": "经核查，系统鉴别数据采用SHA512算法加密，个人信息和业务数据采用AES算法加密，可保证数据存储过程中的保密性。", "is_potential_toc": true}, {"table_index": 85, "row": 2, "cell": 2, "text": "经核查，个人信息采用AES算法加密，身份证照片存储在望海康信云平台的对象存储中，对象存储已经开启了服务端加密特性Server Side Encryption，并采用AES256算法加密，未能保证个人信息存储完整性。", "is_potential_toc": true}, {"table_index": 85, "row": 4, "cell": 2, "text": "经核查，个人信息采用AES算法加密，身份证照片存储在望海康信云平台的对象存储中，对象存储已经开启了服务端加密特性Server Side Encryption，并采用AES256算法加密，可保证数据存储过程中的保密性。", "is_potential_toc": true}, {"table_index": 87, "row": 10, "cell": 2, "text": "经核查，系统部署在私有云望海康信云平台，望海康信云平台提供有ELK日志审计服务，收集和存储设备日志进行集中管理和分析，根据分析结果进行处理，根据安全审计策略对审计记录进行存储、管理和查询等，留存6个月以上的日志记录。", "is_potential_toc": true}, {"table_index": 89, "row": 7, "cell": 2, "text": "经访谈安全管理员，每年对安全管理制度的合理性和适用性进行审定，提供了制度修订会议纪要《2024年安全管理制度修订及评审》。", "is_potential_toc": true}, {"table_index": 90, "row": 7, "cell": 2, "text": "经核查，在《第三方人员安全管理规定》、《网络安全管理规定》、《变更管理规定》中，已针对系统变更、重要操作、物理访问和系统接入等事项建立了逐级审批程序，抽查了《申请定制服务器20241206》的审批记录，与制度要求一致。", "is_potential_toc": true}, {"table_index": 90, "row": 14, "cell": 2, "text": "经核查，根据制定的安全检查表格，进行安全检查，并根据安全检查结果形成了安全检查报告，检查结果上报部门领导，通过内部邮件系统进行通报，具有《信息安全检查记录》《信息安全检查报告》和邮件《2024年度望海北京信息安全检查报告》的通报记录。", "is_potential_toc": true}, {"table_index": 91, "row": 6, "cell": 2, "text": "在《信息化人员管理规定》中，明确了开展安全教育和培训的管理规定；针对不同岗位制定了不同的培训计划，具有《项目信息安全培训2024》，内容包含对信息安全基础知识、岗位操作规程等进行培训的计划、周期、方式、考核等，并对安全责任和惩戒措施进行了明确。", "is_potential_toc": true}, {"table_index": 91, "row": 7, "cell": 2, "text": "在《信息化人员管理规定》中，明确了针对不同岗位制定了不同的培训计划，公司员工通过望海网络学院进行信息安全培训以及技能培训，并进行考核。核查了年度《项目信息安全培训2024》，培训内容覆盖了安全基础知识、岗位操作规程等；安全教育和培训记录在望海网络学院平台留存。现场核查了《安全培训20240924》、《培训Nginx配置和使用20240927》等培训记录，含培训内容、人员及考核记录等。", "is_potential_toc": true}, {"table_index": 91, "row": 8, "cell": 2, "text": "经核查，每年针对不同岗位的人员至少进行一次技能考核，提供了公司员工的考核记录《信息安全考核20241111》。", "is_potential_toc": true}, {"table_index": 92, "row": 4, "cell": 2, "text": "已在东城公安机关备案，具备公安机关出具的备案证明11010113074-20001。", "is_potential_toc": true}, {"table_index": 92, "row": 9, "cell": 2, "text": "经访谈及核查，被测系统采用的密码产品为深信服 aTrust-1000-S1060G VPN，具有国家密码局颁发的商用密码产品型号证书，证书编号为GM004410620210371。", "is_potential_toc": true}, {"table_index": 94, "row": 13, "cell": 2, "text": "经核查，每月进行一次漏洞扫描，现场检查了2024年11月扫描报告，已将所发现的漏洞已发放给相关负责人进行修改，具有漏洞整改报告。", "is_potential_toc": true}, {"table_index": 94, "row": 14, "cell": 2, "text": "系统定级为三级系统，按照要求每年进行了等保测评，提供了2023年的等保测评报告及整改加固实施说明。", "is_potential_toc": true}, {"table_index": 94, "row": 29, "cell": 2, "text": "经访谈，所使用密码产品为深信服 aTrust-1000-S1060G VPN，具有国家密码局颁发的商用密码产品型号证书，账号密码的设定符合国家和行业标准要求。", "is_potential_toc": true}, {"table_index": 94, "row": 30, "cell": 2, "text": "经访谈，所使用密码产品为深信服 aTrust-1000-S1060G VPN，已经过国家密码管理主管部门认证核准。", "is_potential_toc": true}, {"table_index": 94, "row": 43, "cell": 2, "text": "对系统相关的人员每年进行至少一次应急预案培训和演练，提供了《数据异常、配置备份恢复培训》、《数据异常、配置备份恢复演练报告20240619》，内容包含了应急培训对象、人员，培训内容，应急演练时间、主要操作内容、演练结果等信息。", "is_potential_toc": true}, {"table_index": 94, "row": 44, "cell": 2, "text": "经核查，每年对应急预案进行评估修订，提供了《2024年安全管理制度修订及评审》，内容包括修订内容，修订时间等。", "is_potential_toc": true}, {"table_index": 96, "row": 1, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 96, "row": 2, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 96, "row": 3, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 96, "row": 4, "cell": 2, "text": "slb-03", "is_potential_toc": true}, {"table_index": 96, "row": 5, "cell": 2, "text": "db-84，db-85，db-86，mq-03，mq-04", "is_potential_toc": true}, {"table_index": 97, "row": 1, "cell": 5, "text": "1）机房出入口无任何访问控制措施，例如未安装电子或机械门锁（包括机房大门处于未上锁状态），且机房门口无专人值守等；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 2, "cell": 5, "text": "1）室外控制设备裸露放置、无固定措施，无法做到防水、防盗和防火；\n2）放置控制设备的箱体或装置不具有相关检测验收报告，不具有透风、散热、防盗、防雨和防火能力；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 3, "cell": 5, "text": "1）网络设备、安全设备等网络链路上的关键设备性能无法满足高峰期需求；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 4, "cell": 5, "text": "1）云计算平台未采取网络隔离技术实现不同云服务客户虚拟网络之间的隔离；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 5, "cell": 5, "text": "1）云计算平台无法提供满足云服务客户业务需要的通信传输、边界防护、入侵防范等安全机制；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 6, "cell": 5, "text": "1）工业控制系统所在的生产网络未根据不同工业控制系统业务特点划分不同安全域；\n2）不同安全域之间无任何访问控制措施或访问控制措施无效；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 7, "cell": 5, "text": "1）未使用基于密码技术的身份鉴别技术；\n2）未使用密码技术保证数据传输过程中的完整性和保密性；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 8, "cell": 5, "text": "1）网络边界链路接入设备未配置明确的上联链路端口；\n2）网络边界链路接入设备端口IP及路由策略不明确；\n3）网络边界链路接入设备未关闭不使用的端口；\n4）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 9, "cell": 5, "text": "1）内部重要网络与无线网络互联，且不通过任何受控的边界设备或边界设备访问控制策略设置不当；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 10, "cell": 5, "text": "1）重要网络区域与其他网络区域之间（包括内部区域边界和外部区域边界）访问控制设备缺失或访问控制措施失效；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 11, "cell": 5, "text": "1）关键网络节点无任何网络攻击行为检测手段和防护手段；\n2）网络攻击行为检测措施的策略库/规则库半年及以上未更新；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 12, "cell": 5, "text": "1）关键网络节点无任何网络攻击行为检测手段和防护手段；\n2）网络攻击行为检测措施的策略库/规则库半年及以上未更新；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 13, "cell": 5, "text": "1）关键网络节点对新型网络攻击行为网络访问行为无任何分析手段，例如未部署抗APT系统、全流量分析系统、沙箱系统、具备行为分析功能的态势感知系统、威胁情报系统等；\n2）基于威胁情报、行为分析模型进行行为分析的安全措施，半年及以上未更新威胁情报库、事件分析模型，无法满足防护需求；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 14, "cell": 5, "text": "1）云计算平台对云服务客户发起的网络攻击行为无任何检测手段；\n2）网络攻击行为检测措施的策略库/规则库半年及以上未更新；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 15, "cell": 5, "text": "1）感知节点设备（例如感知节点接入网关、传感器等设备）未经授权可以接入网络；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 16, "cell": 5, "text": "1）存在可登录的空口令账户、无身份鉴别措施或身份鉴别措施可被绕过等；\n2）存在可登录的弱口令账户，如长度在6位以下，或存在单个、相同、连续数字/字母/字符及常见字典等易猜测的口令；\n3）多个不同被测对象的管理账户口令相同、或同一被测对象中多个不同管理账户口令相同；\n4）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 17, "cell": 5, "text": "1）未采用两种或两种以上组合鉴别技术对用户进行身份鉴别；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 18, "cell": 5, "text": "1）业务应用系统/平台访问控制策略存在缺陷或不完善，可非授权访问系统功能模块或查看、操作其他用户的数据；\n2）数据库管理系统访问控制策略存在缺陷或不完善，存在数据库的超级管理员权限被授予非数据库管理员用户、或数据库的默认超级管理员账户被用于非数据库管理用途；\n3）操作系统访问控制策略存在缺陷或不完善。存在以操作系统超级管理员权限运行的数据库、中间件、应用程序等非操作系统级进程或服务；\n4）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 19, "cell": 5, "text": "1）开启多余的系统服务、默认共享和高危端口，且可被远程访问；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 20, "cell": 5, "text": "1）通过互联网管理或访问的系统或设备，存在外界披露的可被利用的高危安全漏洞；\n2）通过非互联网等公共通信网络管理或访问的系统或设备，经测试验证确认存在缓冲区溢出、越权访问、远程代码执行等可被利用的高危安全漏洞；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 21, "cell": 5, "text": "1）未采取恶意代码检测和清除措施；\n2）恶意代码防范产品授权已过期；\n3）恶意代码特征库、规则库等一个月以上未更新；\n4）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 22, "cell": 5, "text": "1）鉴别信息、重要个人信息或重要业务数据等以明文的方式在网络环境中传输；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 23, "cell": 5, "text": "1）鉴别信息、重要个人信息、具有保密性需求的重要业务数据以明文的方式存储；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 24, "cell": 5, "text": "1）重要数据未提供任何数据备份措施；\n2）重要数据备份到互联网网盘或相关存储环境；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 25, "cell": 5, "text": "1）获取了最高管理员权限或子管理员权限或普通用户权限；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 26, "cell": 5, "text": "1）获取了被测系统权限；\n2）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 27, "cell": 5, "text": "1）大型专网未能有效收口；\n2）移动应用后台接口成为新的暴露点；\n3）应用私搭乱建，管理不到位，存在防护盲区；\n4）违规打通内网与互联网连接；\n5）第三方接入缺乏安全管理与整体管控；\n6）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 28, "cell": 5, "text": "1）违背最小化原则信息量泄露过多；\n2）数据传输、存储、备份不规范；\n3）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 29, "cell": 5, "text": "1）集权系统鉴别机制存在缺陷；\n2）管理后台自身架构存在漏洞；\n3）集权系统支撑环境存在漏洞；\n4）域控系统缺乏重点防护措施；\n5）其他。", "is_potential_toc": true}, {"table_index": 97, "row": 30, "cell": 5, "text": "1）供应链管控不力，成为迂回攻击跳板；\n2）网络资产底数不清晰，老旧资产、测试系统未下线；\n3）基层单位安全防护能力薄弱，极易成为攻击突破口；\n4）员工安全意识不足，易被社工攻击利用；\n5）公开漏洞未修复；\n6）0Day漏洞攻击难以监测发现；\n7）网络安全监测存在盲点导致难以及时发现隐蔽入侵行为，缺乏对攻击监测数据的分析；\n8）其他。", "is_potential_toc": true}, {"table_index": 99, "row": 1, "cell": 5, "text": "第三级（S3A3）", "is_potential_toc": true}, {"table_index": 99, "row": 4, "cell": 1, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信云平台的物理机房、网络架构、网络设备、安全设备、系统管理软件、主机操作系统、数据库管理系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备物理位置选择、物理访问控制、防盗窃和防破坏、防雷击、防火、防静电、温湿度控制、电力供应、基础设施位置、网络架构、通信传输、访问控制、入侵防范、访问控制、入侵防范、恶意代码防范、剩余信息保护、身份鉴别、入侵防范、镜像和快照保护、剩余信息保护、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、安全方案设计、自行软件开发、测试验收、系统交付、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理、云计算环境管理等安全管理措施。\n本次安全等级测评共发现望海康信云平台存在高风险问题0个，中风险问题25个，低风险问题12个。望海康信云平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 99, "row": 4, "cell": 2, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信云平台的物理机房、网络架构、网络设备、安全设备、系统管理软件、主机操作系统、数据库管理系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备物理位置选择、物理访问控制、防盗窃和防破坏、防雷击、防火、防静电、温湿度控制、电力供应、基础设施位置、网络架构、通信传输、访问控制、入侵防范、访问控制、入侵防范、恶意代码防范、剩余信息保护、身份鉴别、入侵防范、镜像和快照保护、剩余信息保护、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、安全方案设计、自行软件开发、测试验收、系统交付、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理、云计算环境管理等安全管理措施。\n本次安全等级测评共发现望海康信云平台存在高风险问题0个，中风险问题25个，低风险问题12个。望海康信云平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 99, "row": 4, "cell": 3, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信云平台的物理机房、网络架构、网络设备、安全设备、系统管理软件、主机操作系统、数据库管理系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备物理位置选择、物理访问控制、防盗窃和防破坏、防雷击、防火、防静电、温湿度控制、电力供应、基础设施位置、网络架构、通信传输、访问控制、入侵防范、访问控制、入侵防范、恶意代码防范、剩余信息保护、身份鉴别、入侵防范、镜像和快照保护、剩余信息保护、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、安全方案设计、自行软件开发、测试验收、系统交付、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理、云计算环境管理等安全管理措施。\n本次安全等级测评共发现望海康信云平台存在高风险问题0个，中风险问题25个，低风险问题12个。望海康信云平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 99, "row": 4, "cell": 4, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信云平台的物理机房、网络架构、网络设备、安全设备、系统管理软件、主机操作系统、数据库管理系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备物理位置选择、物理访问控制、防盗窃和防破坏、防雷击、防火、防静电、温湿度控制、电力供应、基础设施位置、网络架构、通信传输、访问控制、入侵防范、访问控制、入侵防范、恶意代码防范、剩余信息保护、身份鉴别、入侵防范、镜像和快照保护、剩余信息保护、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、安全方案设计、自行软件开发、测试验收、系统交付、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理、云计算环境管理等安全管理措施。\n本次安全等级测评共发现望海康信云平台存在高风险问题0个，中风险问题25个，低风险问题12个。望海康信云平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 99, "row": 4, "cell": 5, "text": "本次安全等级测评采用访谈、核查和测试等测评方法，对望海康信云平台的物理机房、网络架构、网络设备、安全设备、系统管理软件、主机操作系统、数据库管理系统、数据资源、管理制度等具体对象实施了测评。通过测评发现，在安全技术方面具备物理位置选择、物理访问控制、防盗窃和防破坏、防雷击、防火、防静电、温湿度控制、电力供应、基础设施位置、网络架构、通信传输、访问控制、入侵防范、访问控制、入侵防范、恶意代码防范、剩余信息保护、身份鉴别、入侵防范、镜像和快照保护、剩余信息保护、系统管理、审计管理、安全管理等安全技术措施。在安全管理方面具备安全策略、管理制度、制定和发布、评审和修订、岗位设置、人员配备、沟通和合作、审核和检查、人员录用、人员离岗、安全意识教育和培训、外部人员访问管理、定级和备案、安全方案设计、自行软件开发、测试验收、系统交付、环境管理、资产管理、设备维护管理、漏洞和风险管理、配置管理、密码管理、变更管理、备份与恢复管理、云计算环境管理等安全管理措施。\n本次安全等级测评共发现望海康信云平台存在高风险问题0个，中风险问题25个，低风险问题12个。望海康信云平台技术方面尚有一些问题需要整改和完善，信息安全管理体系执行情况还可以进一步提高。", "is_potential_toc": true}, {"table_index": 101, "row": 8, "cell": 3, "text": "部分整改，数据库采用用户名+口令方式进行登录，身份标识唯一，无空口令账户，具备口令复杂度要求，口令长度要求8位以上，由大小写字母+数字+特殊字符组成，但未配置口令定期更换策略。", "is_potential_toc": true}, {"table_index": 101, "row": 13, "cell": 3, "text": "通过display current-configuration查看运行配置，设置了ssh server acl 2000，SSH远程登录引用2000访问控制列表，限制管理员登录地址。", "is_potential_toc": true}, {"table_index": 101, "row": 16, "cell": 1, "text": "本地留存有6个月以上的日志记录，但未配置日志服务器。", "is_potential_toc": true}, {"table_index": 101, "row": 21, "cell": 1, "text": "用户鉴别信息使用MD5进行加密，重要的业务信息未进行加密处理，不能保证存储过程中的完整性。", "is_potential_toc": true}, {"table_index": 101, "row": 31, "cell": 3, "text": "每年对安全管理制度的合理性和适用性进行审定，提供了制度修订会议纪要《2024年安全管理制度修订及评审》。", "is_potential_toc": true}, {"table_index": 101, "row": 33, "cell": 3, "text": "每年针对不同岗位的人员至少进行一次技能考核，提供了公司员工的考核记录《信息安全考核20241111》。", "is_potential_toc": true}, {"table_index": 101, "row": 34, "cell": 3, "text": "被测系统采用的密码产品为深信服 aTrust-1000-S1060G VPN，具有国家密码局颁发的商用密码产品型号证书，证书编号为GM004410620210371。", "is_potential_toc": true}, {"table_index": 101, "row": 43, "cell": 3, "text": "所使用密码产品为深信服 aTrust-1000-S1060G VPN，具有国家密码局颁发的商用密码产品型号证书，账号密码的设定符合国家和行业标准要求。", "is_potential_toc": true}, {"table_index": 101, "row": 44, "cell": 3, "text": "所使用密码产品为深信服 aTrust-1000-S1060G VPN，已经过国家密码管理主管部门认证核准。", "is_potential_toc": true}, {"table_index": 101, "row": 45, "cell": 3, "text": "每年对应急预案进行评估修订，提供了《2024年安全管理制度修订及评审》，内容包括修订内容，修订时间等。", "is_potential_toc": true}]}