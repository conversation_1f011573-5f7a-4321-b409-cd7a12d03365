#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进后的Word文档提取功能
验证标题匹配和内容边界的准确性
"""

import requests
import os
import json

def test_api_extraction():
    """
    测试API提取功能
    """
    print("🧪 测试改进后的Word文档提取API")
    print("="*60)
    
    # 检查源文档
    source_file = "测评报告.docx"
    if not os.path.exists(source_file):
        print(f"❌ 源文档不存在: {source_file}")
        return False
    
    # API基础URL
    base_url = "http://localhost:5000/api/wordextraction"
    
    try:
        # 1. 测试文档分析
        print("📋 1. 测试文档分析...")
        with open(source_file, 'rb') as f:
            files = {'file': f}
            response = requests.post(f"{base_url}/analyze", files=files)
        
        if response.status_code == 200:
            analysis = response.json()
            print(f"✅ 分析成功!")
            print(f"   - 总段落数: {analysis['totalParagraphs']}")
            print(f"   - 总表格数: {analysis['totalTables']}")
            print(f"   - 标题数量: {len(analysis['headings'])}")
            print(f"   - 目录项数: {len(analysis['tocEntries'])}")
            
            # 显示前10个目录项
            print(f"\n📑 前10个目录项:")
            for i, entry in enumerate(analysis['tocEntries'][:10]):
                title = entry['title']
                page = entry['pageNumber']
                level = entry['level']
                indent = "  " * (level - 1)
                print(f"   {indent}• {title} -> 第{page}页")
            
        else:
            print(f"❌ 分析失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            return False
        
        # 2. 测试精确提取
        print(f"\n🎯 2. 测试精确提取...")
        test_titles = [
            "网络安全等级测评基本信息表",
            "测评项目概述",
            "被测对象描述"
        ]
        
        for title in test_titles:
            print(f"\n   测试标题: {title}")
            
            with open(source_file, 'rb') as f:
                files = {'file': f}
                data = {
                    'TargetTitle': title,
                    'PreserveFormatting': 'true',
                    'IncludeTables': 'true'
                }
                response = requests.post(f"{base_url}/extract", files=files, data=data)
            
            if response.status_code == 200:
                # 保存提取的文档
                output_file = f"提取测试_{title.replace('/', '_').replace('\\', '_').replace(':', '_')}.docx"
                with open(output_file, 'wb') as f:
                    f.write(response.content)
                
                print(f"   ✅ 提取成功: {output_file}")
                
                # 验证文档大小
                file_size = os.path.getsize(output_file)
                print(f"   📄 文件大小: {file_size:,} 字节")
                
                if file_size < 1000:
                    print(f"   ⚠️  警告: 文件太小，可能提取内容不完整")
                
            else:
                print(f"   ❌ 提取失败: {response.status_code}")
                print(f"   错误信息: {response.text}")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web服务器")
        print("💡 请确保Web应用程序正在运行 (dotnet run)")
        return False
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

def test_python_extraction():
    """
    对比测试Python版本的提取
    """
    print(f"\n🐍 对比测试Python版本...")
    
    try:
        # 运行Python提取脚本
        import subprocess
        result = subprocess.run(['python', 'demo_extraction.py'], 
                              capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ Python版本提取成功")
            print("📄 生成文件: 网络安全等级测评基本信息表_提取结果.docx")
        else:
            print(f"❌ Python版本提取失败")
            print(f"错误信息: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Python版本测试出错: {str(e)}")

def compare_results():
    """
    比较不同版本的提取结果
    """
    print(f"\n📊 比较提取结果...")
    
    files_to_compare = [
        "提取测试_网络安全等级测评基本信息表.docx",
        "网络安全等级测评基本信息表_提取结果.docx",
        "网络安全等级测评基本信息表.docx"
    ]
    
    print("📋 文件大小对比:")
    for file_path in files_to_compare:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   • {file_path}: {size:,} 字节")
        else:
            print(f"   • {file_path}: 文件不存在")

def main():
    """
    主测试函数
    """
    print("🚀 Word文档提取功能测试")
    print("="*60)
    
    # 测试API版本
    api_success = test_api_extraction()
    
    if api_success:
        # 测试Python版本
        test_python_extraction()
        
        # 比较结果
        compare_results()
        
        print(f"\n🎉 测试完成!")
        print(f"💡 改进要点:")
        print(f"   1. 清理了Word内部引用 (PAGEREF等)")
        print(f"   2. 改进了标题匹配逻辑")
        print(f"   3. 优化了内容边界检测")
        print(f"   4. 增强了表格关联算法")
        
    else:
        print(f"\n❌ API测试失败，请检查Web服务器状态")

if __name__ == "__main__":
    main()
