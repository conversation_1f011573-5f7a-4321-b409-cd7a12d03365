# 安装指南

## 系统要求检查

当前系统缺少 .NET 8 SDK，需要先安装才能运行C# Web应用程序。

## 安装 .NET 8 SDK

### 方法1: 从Microsoft官网下载

1. 访问 [.NET 8 下载页面](https://dotnet.microsoft.com/download/dotnet/8.0)
2. 选择 "SDK" 版本（不是Runtime）
3. 根据您的系统选择对应版本：
   - Windows x64
   - Windows x86
   - Windows Arm64

### 方法2: 使用winget安装（推荐）

如果您的系统支持winget，可以使用以下命令：

```powershell
winget install Microsoft.DotNet.SDK.8
```

### 方法3: 使用Chocolatey安装

如果您安装了Chocolatey包管理器：

```powershell
choco install dotnet-8.0-sdk
```

## 验证安装

安装完成后，重新打开命令提示符或PowerShell，运行：

```bash
dotnet --version
```

应该显示类似 `8.0.xxx` 的版本号。

## 运行应用程序

安装.NET SDK后，可以通过以下方式运行：

### 方法1: 使用批处理文件
双击 `run.bat` 文件

### 方法2: 手动命令
```bash
dotnet restore
dotnet build
dotnet run
```

## 替代方案

如果无法安装.NET SDK，我们提供了以下替代方案：

### 1. Python版本（已实现）
- 使用 `extract_specific_section.py` 脚本
- 功能：提取指定标题内容，保留基本格式
- 运行：`python extract_specific_section.py`

### 2. 在线版本部署
我们可以将C# Web应用程序部署到云平台，您可以通过浏览器直接使用。

## 功能对比

| 功能 | Python版本 | C# Web版本 |
|------|------------|------------|
| 文档分析 | ✅ | ✅ |
| 目录提取 | ✅ | ✅ |
| 内容提取 | ✅ | ✅ |
| 格式保留 | 基本 | 完全 |
| Web界面 | ❌ | ✅ |
| 批量处理 | ❌ | ✅ |
| API接口 | ❌ | ✅ |

## 推荐使用方案

1. **开发环境**: 安装.NET SDK，使用C# Web版本
2. **快速使用**: 使用Python版本进行基本提取
3. **生产环境**: 部署C# Web版本到服务器

## 技术支持

如果在安装过程中遇到问题，请：

1. 检查系统是否为Windows 10/11
2. 确认有管理员权限
3. 检查网络连接
4. 查看Windows更新状态

## 下一步

安装完成.NET SDK后，请：

1. 重启命令提示符/PowerShell
2. 验证 `dotnet --version` 命令
3. 运行 `run.bat` 启动应用程序
4. 在浏览器中访问 http://localhost:5000
